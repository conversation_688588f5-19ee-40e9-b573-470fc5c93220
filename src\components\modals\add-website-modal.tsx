'use client';

import { useState, useEffect, useCallback, memo } from 'react';
import { X, Plus, Globe, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { useWebsites } from '@/stores/websites-store';
import { useCategories } from '@/stores/categories-store';
import { PREDEFINED_WEBSITES } from '@/constants';
import { isValidUrl, normalizeUrl, getFaviconFallbacks } from '@/utils';
import { PreDefinedWebsite, Website } from '@/types';
import React from 'react';

// Enhanced Favicon component with multiple fallbacks
interface FaviconImageProps {
  url: string;
  storedFaviconUrl?: string;
  alt: string;
  className?: string;
}

const FaviconImage = memo(function FaviconImage({ 
  url, 
  storedFaviconUrl,
  alt, 
  className = "w-6 h-6 rounded-sm" 
}: FaviconImageProps) {
  const [currentFaviconIndex, setCurrentFaviconIndex] = useState(0);
  const [hasError, setHasError] = useState(false);
  
  // Create fallback URLs, using stored favicon first if available
  const fallbackUrls = React.useMemo(() => {
    const generatedFallbacks = getFaviconFallbacks(url);
    return storedFaviconUrl 
      ? [storedFaviconUrl, ...generatedFallbacks.filter(u => u !== storedFaviconUrl)]
      : generatedFallbacks;
  }, [url, storedFaviconUrl]);
  
  const currentFaviconUrl = fallbackUrls[currentFaviconIndex] || '';

  const handleError = useCallback(() => {
    if (currentFaviconIndex < fallbackUrls.length - 1) {
      setCurrentFaviconIndex(currentFaviconIndex + 1);
    } else {
      setHasError(true);
    }
  }, [currentFaviconIndex, fallbackUrls.length]);

  const handleLoad = useCallback(() => {
    setHasError(false);
  }, []);

  // Reset when URLs change
  React.useEffect(() => {
    setCurrentFaviconIndex(0);
    setHasError(false);
  }, [url, storedFaviconUrl]);

  if (hasError || !currentFaviconUrl) {
    return (
      <div className={`${className} bg-muted rounded-sm flex items-center justify-center`}>
        <Globe className="h-3 w-3 text-muted-foreground" />
      </div>
    );
  }

  return (
    /* eslint-disable-next-line @next/next/no-img-element */
    <img 
      src={currentFaviconUrl}
      alt={alt}
      className={className}
      onError={handleError}
      onLoad={handleLoad}
    />
  );
});

interface AddWebsiteModalProps {
  isOpen: boolean;
  onClose: () => void;
  categoryId?: string;
  prefilledData?: Partial<Website> | null;
}

export function AddWebsiteModal({ isOpen, onClose, categoryId, prefilledData }: AddWebsiteModalProps) {
  const [activeTab, setActiveTab] = useState<'predefined' | 'custom'>('predefined');
  const [searchQuery, setSearchQuery] = useState('');
  const [customUrl, setCustomUrl] = useState('');
  const [customName, setCustomName] = useState('');
  const [customDescription, setCustomDescription] = useState('');
  const [selectedCategoryId, setSelectedCategoryId] = useState(categoryId || '');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { addWebsite, getWebsitesByCategory, error: websiteError } = useWebsites();
  const { categories } = useCategories();

  // Reset form when modal opens/closes or when prefilled data changes
  useEffect(() => {
    if (isOpen) {
      // If prefilled data is provided, use it and switch to custom tab
      if (prefilledData) {
        setActiveTab('custom');
        setCustomName(prefilledData.name || '');
        setCustomUrl(prefilledData.url || '');
        setCustomDescription(prefilledData.description || '');
        setSearchQuery('');
      } else {
        setActiveTab('predefined');
        setCustomName('');
        setCustomUrl('');
        setCustomDescription('');
        setSearchQuery('');
      }
      setSelectedCategoryId(categoryId || '');
      setError('');
      setIsSubmitting(false);
    }
  }, [isOpen, categoryId, prefilledData]);

  // Display website error from store
  useEffect(() => {
    if (websiteError) {
      setError(websiteError);
    }
  }, [websiteError]);

  if (!isOpen) return null;

  // Filter predefined websites based on search
  const filteredPredefinedWebsites = PREDEFINED_WEBSITES.filter(website =>
    website.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    website.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    website.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleAddPredefinedWebsite = async (website: PreDefinedWebsite) => {
    if (!selectedCategoryId) {
      setError('Please select a category');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      // Get websites in the selected category to determine order
      const websitesInCategory = getWebsitesByCategory(selectedCategoryId);
      
      addWebsite({
        name: website.name,
        url: website.url,
        description: website.description,
        categoryId: selectedCategoryId,
        order: websitesInCategory.length,
      });

      // Close modal on success
      if (!websiteError) {
        onClose();
      }
    } catch {
      setError('Failed to add website');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddCustomWebsite = async () => {
    if (!customName.trim()) {
      setError('Website name is required');
      return;
    }

    if (!customUrl.trim()) {
      setError('Website URL is required');
      return;
    }

    if (!isValidUrl(customUrl)) {
      setError('Please enter a valid URL (e.g., https://example.com)');
      return;
    }

    if (!selectedCategoryId) {
      setError('Please select a category');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      // Get websites in the selected category to determine order
      const websitesInCategory = getWebsitesByCategory(selectedCategoryId);
      
      addWebsite({
        name: customName.trim(),
        url: normalizeUrl(customUrl),
        description: customDescription.trim() || undefined,
        faviconUrl: prefilledData?.faviconUrl,
        categoryId: selectedCategoryId,
        order: websitesInCategory.length,
      });

      // Close modal on success
      if (!websiteError) {
        onClose();
      }
    } catch {
      setError('Failed to add website');
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedCategory = categories.find(cat => cat.id === selectedCategoryId);

  return (
    <div 
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60] p-4"
      onClick={onClose}
    >
      <div 
        className="bg-background sketchy-box-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold">Add Website</h2>
            <p className="text-sm text-muted-foreground mt-1">
              Choose from popular websites or add a custom URL
            </p>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Category Selection */}
        <div className="p-6 border-b bg-muted/30">
          <div className="space-y-2">
            <label className="text-sm font-medium">Add to Category:</label>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategoryId(category.id)}
                  className={`px-3 py-1.5 text-sm font-medium transition-colors ${
                    selectedCategoryId === category.id
                      ? 'bg-primary text-primary-foreground sketchy-box-sm'
                      : 'bg-background border hover:bg-accent sketchy-box-sm'
                  }`}
                  style={{
                    borderColor: selectedCategoryId === category.id ? undefined : category.color,
                  }}
                >
                  {category.name}
                </button>
              ))}
            </div>
            {selectedCategory && (
              <p className="text-xs text-muted-foreground">
                Adding to &quot;{selectedCategory.name}&quot; category
              </p>
            )}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="p-4 bg-destructive/10 text-destructive text-sm border-b">
            {error}
          </div>
        )}

        {/* Tabs */}
        <div className="flex border-b">
          <button
            onClick={() => setActiveTab('predefined')}
            className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'predefined'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground'
            }`}
          >
            Popular Websites
          </button>
          <button
            onClick={() => setActiveTab('custom')}
            className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'custom'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground'
            }`}
          >
            Custom URL
          </button>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[500px] overflow-y-auto">
          {activeTab === 'predefined' ? (
            <div className="space-y-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search popular websites..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Website Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {filteredPredefinedWebsites.map((website) => (
                  <Card
                    key={`${website.name}-${website.url}`}
                    className="hover:shadow-md transition-shadow cursor-pointer group sketchy-box"
                    onClick={() => handleAddPredefinedWebsite(website)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-muted sketchy-box-sm flex items-center justify-center flex-shrink-0">
                          <FaviconImage url={website.url} alt={website.name} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-sm group-hover:text-primary transition-colors">
                            {website.name}
                          </h4>
                          <p className="text-xs text-muted-foreground truncate">
                            {website.description}
                          </p>
                          <p className="text-xs text-muted-foreground/70 truncate">
                            {website.url}
                          </p>
                        </div>
                        <Plus className="h-4 w-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {filteredPredefinedWebsites.length === 0 && (
                <div className="text-center py-8">
                  <Globe className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No websites found</p>
                  <p className="text-sm text-muted-foreground/70">
                    Try a different search term or add a custom URL
                  </p>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Website Name</label>
                <Input
                  placeholder="e.g., My Portfolio"
                  value={customName}
                  onChange={(e) => setCustomName(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Website URL</label>
                <Input
                  placeholder="https://example.com"
                  value={customUrl}
                  onChange={(e) => setCustomUrl(e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  Include the full URL with https:// or http://
                </p>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Description (Optional)</label>
                <Input
                  placeholder="Brief description of the website"
                  value={customDescription}
                  onChange={(e) => setCustomDescription(e.target.value)}
                />
              </div>
              <Button
                onClick={handleAddCustomWebsite}
                disabled={isSubmitting || !customName.trim() || !customUrl.trim() || !selectedCategoryId}
                className="w-full"
              >
                {isSubmitting ? 'Adding...' : 'Add Website'}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 