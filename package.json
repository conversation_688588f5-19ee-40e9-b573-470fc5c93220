{"name": "stackzest", "version": "0.1.0", "private": true, "description": "A personalized tech stack organizer that helps users stay up-to-date with the latest technologies", "author": "StackZest Team", "keywords": ["tech-stack", "organizer", "productivity", "nextjs", "react", "typescript"], "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@stagewise/toolbar-next": "^0.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.12.2", "lucide-react": "^0.511.0", "next": "15.1.8", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20.17.50", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.8", "husky": "^9.1.7", "lint-staged": "^16.0.0", "postcss": "^8", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.1", "typescript": "^5"}}