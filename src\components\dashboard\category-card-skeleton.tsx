'use client';

import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export function CategoryCardSkeleton() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="h-fit"
    >
      <Card className="group backdrop-blur-sm transition-all duration-300">
        <CardHeader className="pb-3 border-b transition-all duration-300">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Skeleton className="p-2 rounded-md w-9 h-9" />
              <Skeleton className="h-6 w-24" />
            </div>
            <Skeleton className="w-8 h-8 rounded-md" />
          </div>
        </CardHeader>

        <CardContent className="transition-all duration-300">
          <div className="space-y-2 pt-4">
            {/* Skeleton for 3 website items */}
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex items-center space-x-2 p-2">
                <Skeleton className="w-4 h-4 rounded-sm" />
                <Skeleton className="h-4 flex-1" />
                <Skeleton className="w-4 h-4" />
              </div>
            ))}
            
            {/* Skeleton for add button */}
            <div className="pt-2">
              <Skeleton className="w-full h-8 rounded-md" />
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
} 