'use client';

import React, { useCallback, memo, useState } from 'react';
import { motion } from 'framer-motion';
import { ExternalLink, GripVertical, Globe } from 'lucide-react';
import { Website } from '@/types';
import { WebsiteActionsMenu } from '@/components/ui/website-actions-menu';
import { useDragDrop } from './drag-drop-provider';
import { useCategoryDrag } from './animated-category-card';
import { getFaviconFallbacks } from '@/utils';

interface DraggableWebsiteCardProps {
  website: Website;
  onOpen: (url: string) => void;
  onDelete: (websiteId: string) => void;
  onEdit?: (website: Website) => void;
  onMove?: (websiteId: string, targetCategoryId: string) => void;
  onReorder?: (websiteId: string, targetWebsiteId: string, position: 'before' | 'after') => void;
  onMoveToPosition?: (websiteId: string, targetCategoryId: string, targetWebsiteId: string, position: 'before' | 'after') => void;
}

// Enhanced Favicon component with multiple fallbacks
interface FaviconImageProps {
  url: string;
  storedFaviconUrl?: string;
  alt: string;
  className?: string;
}

const FaviconImage = memo(function FaviconImage({ 
  url, 
  storedFaviconUrl,
  alt, 
  className = "w-6 h-6 rounded-sm" 
}: FaviconImageProps) {
  const [currentFaviconIndex, setCurrentFaviconIndex] = useState(0);
  const [hasError, setHasError] = useState(false);
  
  // Create fallback URLs, using stored favicon first if available
  const fallbackUrls = React.useMemo(() => {
    const generatedFallbacks = getFaviconFallbacks(url);
    return storedFaviconUrl 
      ? [storedFaviconUrl, ...generatedFallbacks.filter(u => u !== storedFaviconUrl)]
      : generatedFallbacks;
  }, [url, storedFaviconUrl]);
  
  const currentFaviconUrl = fallbackUrls[currentFaviconIndex] || '';

  const handleError = useCallback(() => {
    if (currentFaviconIndex < fallbackUrls.length - 1) {
      setCurrentFaviconIndex(currentFaviconIndex + 1);
    } else {
      setHasError(true);
    }
  }, [currentFaviconIndex, fallbackUrls.length]);

  const handleLoad = useCallback(() => {
    setHasError(false);
  }, []);

  // Reset when URLs change
  React.useEffect(() => {
    setCurrentFaviconIndex(0);
    setHasError(false);
  }, [url, storedFaviconUrl]);

  if (hasError || !currentFaviconUrl) {
    return (
      <div className={`${className} bg-muted rounded-sm flex items-center justify-center`}>
        <Globe className="h-3 w-3 text-muted-foreground" />
      </div>
    );
  }

  return (
    /* eslint-disable-next-line @next/next/no-img-element */
    <img 
      src={currentFaviconUrl}
      alt={alt}
      className={className}
      onError={handleError}
      onLoad={handleLoad}
    />
  );
});

const DraggableWebsiteCard = memo(function DraggableWebsiteCard({
  website,
  onOpen,
  onDelete,
  onEdit,
  onMove,
  onReorder,
  onMoveToPosition,
}: DraggableWebsiteCardProps) {
  const { 
    draggedWebsite, 
    isDragging, 
    startDrag, 
    endDrag,
    dragOverCategoryId,
    dragOverWebsiteId,
    dropPosition,
    handleWebsiteDragOver,
    handleWebsiteDragLeave
  } = useDragDrop();

  const isCategoryBeingDragged = useCategoryDrag();

  const isBeingDragged = draggedWebsite?.id === website.id;
  const isDropTarget = dragOverWebsiteId === website.id;
  const showDropIndicator = isDropTarget && !isBeingDragged && isDragging;

  const handleDragStart = useCallback((e: React.DragEvent) => {
    // Don't start website drag if a category is being dragged
    if (isCategoryBeingDragged) {
      e.preventDefault();
      return;
    }

    console.log('🚀 Drag start:', website.name, website.id);
    e.dataTransfer.setData('text/plain', website.id);
    e.dataTransfer.setData('application/json', JSON.stringify({
      websiteId: website.id,
      categoryId: website.categoryId
    }));
    e.dataTransfer.effectAllowed = 'move';
    startDrag(website);
  }, [isCategoryBeingDragged, website, startDrag]);

  const handleDragEnd = () => {
    // Don't handle drag end if a category is being dragged
    if (isCategoryBeingDragged) {
      return;
    }

    console.log('🏁 Drag end for:', website.name);
    console.log('   dragOverWebsiteId:', dragOverWebsiteId);
    console.log('   dropPosition:', dropPosition);
    console.log('   dragOverCategoryId:', dragOverCategoryId);
    console.log('   draggedWebsite:', draggedWebsite?.name);
    
    // Handle reordering within the same category
    if (dragOverWebsiteId && dropPosition && onReorder && draggedWebsite) {
      console.log('🔄 Calling onReorder:', draggedWebsite.id, dropPosition, dragOverWebsiteId);
      onReorder(draggedWebsite.id, dragOverWebsiteId, dropPosition);
      endDrag();
      return;
    }
    
    // Handle moving between categories
    if (dragOverCategoryId && dragOverCategoryId !== draggedWebsite?.categoryId && onMove && draggedWebsite) {
      console.log('✅ Calling onMove:', draggedWebsite.id, '->', dragOverCategoryId);
      onMove(draggedWebsite.id, dragOverCategoryId);
    }
    
    endDrag();
  };

  const handleDragOver = (e: React.DragEvent) => {
    // Don't handle drag over if a category is being dragged
    if (!isDragging || isBeingDragged || isCategoryBeingDragged) return;
    
    e.preventDefault();
    e.stopPropagation();
    
    // Calculate drop position based on mouse position
    const rect = e.currentTarget.getBoundingClientRect();
    const y = e.clientY - rect.top;
    const height = rect.height;
    const position = y < height / 2 ? 'before' : 'after';
    
    console.log('📍 Drag over:', website.name, 'position:', position);
    handleWebsiteDragOver(website.id, position);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    // Don't handle drag leave if a category is being dragged
    if (isCategoryBeingDragged) return;

    // Only handle drag leave if we're actually leaving this element
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;
    
    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      console.log('🚪 Drag leave:', website.name);
      handleWebsiteDragLeave();
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    // Don't handle drops if a category is being dragged
    if (isCategoryBeingDragged) {
      e.preventDefault();
      return;
    }

    e.preventDefault();
    e.stopPropagation();
    
    console.log('📦 Drop on website:', website.name);
    
    try {
      const dragData = JSON.parse(e.dataTransfer.getData('application/json'));
      const draggedWebsiteId = dragData.websiteId;
      const draggedCategoryId = dragData.categoryId;
      
      console.log('   Dropped website ID:', draggedWebsiteId);
      console.log('   Target website:', website.id);
      console.log('   Drop position:', dropPosition);
      console.log('   Dragged category:', draggedCategoryId);
      console.log('   Target category:', website.categoryId);
      
      if (draggedWebsiteId === website.id) {
        console.log('   Ignoring drop on self');
        return;
      }
      
      // Handle reordering within the same category
      if (draggedCategoryId === website.categoryId && onReorder) {
        const position = dropPosition || 'after';
        console.log('🔄 Drop-triggered reorder (same category):', draggedWebsiteId, position, website.id);
        onReorder(draggedWebsiteId, website.id, position);
      } 
      // Handle cross-category positioning - move to different category at specific position
      else if (draggedCategoryId !== website.categoryId && onMoveToPosition) {
        const position = dropPosition || 'after';
        console.log('🎯 Drop-triggered cross-category positioning:', draggedWebsiteId, '->', website.categoryId, 'at position:', position, 'relative to:', website.id);
        onMoveToPosition(draggedWebsiteId, website.categoryId, website.id, position);
      }
    } catch (error) {
      console.error('Error handling drop:', error);
    }
  };

  const handleClick = useCallback((e: React.MouseEvent) => {
    // Stop propagation to prevent category click
    e.stopPropagation();
    
    // Prevent opening website when dragging
    if (!isDragging && !isCategoryBeingDragged) {
      onOpen(website.url);
    }
  }, [isDragging, isCategoryBeingDragged, onOpen, website.url]);

  // If category is being dragged, render a completely static version
  if (isCategoryBeingDragged) {
    return (
      <div 
        className="relative pointer-events-none select-none"
        style={{ pointerEvents: 'none', userSelect: 'none' }}
      >
        <motion.div
          layout={false}
          className="flex items-center space-x-2 p-2 sketchy-box-sm opacity-75 cursor-default"
          style={{ pointerEvents: 'none', userSelect: 'none' }}
        >
          {/* Favicon */}
          <div className="flex-shrink-0">
            <FaviconImage 
              url={website.url} 
              storedFaviconUrl={website.faviconUrl}
              alt={`${website.name} favicon`} 
            />
          </div>
          {/* Website name */}
          <div className="flex-1 min-w-0">
            <div className="text-lg font-medium truncate group-hover:text-primary transition-colors">
              {website.name}
            </div>
            {website.description && (
              <div className="text-sm text-muted-foreground truncate">
                {website.description}
              </div>
            )}
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Drop indicator - before */}
      {showDropIndicator && dropPosition === 'before' && (
        <motion.div
          initial={{ opacity: 0, scaleX: 0 }}
          animate={{ opacity: 1, scaleX: 1 }}
          className="absolute -top-1 left-0 right-0 h-0.5 bg-primary rounded-full z-10"
        />
      )}
      
      <div
        draggable={true}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <motion.div
          layout
          layoutId={`website-${website.id}`}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ 
            opacity: 1, 
            scale: isBeingDragged ? 1.05 : 1,
            zIndex: isBeingDragged ? 50 : 1
          }}
          exit={{ opacity: 0, scale: 0.8 }}
          whileHover={{ scale: isBeingDragged ? 1.05 : 1.02 }}
          whileTap={{ scale: 0.98 }}
          className={`
            flex items-center space-x-2 p-2 sketchy-box-sm cursor-pointer group transition-all duration-200 relative
            ${isBeingDragged 
              ? 'bg-primary/10 shadow-xl border-2 border-primary/50 z-50' 
              : 'hover:bg-accent/50'
            }
            ${showDropIndicator && dropPosition === 'after' 
              ? 'border-b-2 border-primary/50' 
              : ''
            }
          `}
          onClick={handleClick}
          role="button"
          tabIndex={0}
          aria-label={`Open ${website.name} website`}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleClick();
            }
          }}
        >
          {/* Drag handle */}
          <div className="flex-shrink-0 opacity-0 group-hover:opacity-60 transition-opacity">
            <GripVertical className="h-4 w-4 text-muted-foreground" />
          </div>

          {/* Favicon */}
          <div className="flex-shrink-0">
            <FaviconImage 
              url={website.url} 
              storedFaviconUrl={website.faviconUrl}
              alt={`${website.name} favicon`} 
            />
          </div>
          
          {/* Website name */}
          <div className="flex-1 min-w-0">
            <div className="text-lg font-medium truncate group-hover:text-primary transition-colors">
              {website.name}
            </div>
            {website.description && (
              <div className="text-sm text-muted-foreground truncate">
                {website.description}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <ExternalLink className="h-4 w-4 text-muted-foreground" />
            <WebsiteActionsMenu
              website={website}
              onOpen={onOpen}
              onDelete={onDelete}
              onEdit={onEdit}
            />
          </div>

          {/* Drag indicator */}
          {isBeingDragged && (
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full border-2 border-background"
            />
          )}
        </motion.div>
      </div>

      {/* Drop indicator - after */}
      {showDropIndicator && dropPosition === 'after' && (
        <motion.div
          initial={{ opacity: 0, scaleX: 0 }}
          animate={{ opacity: 1, scaleX: 1 }}
          className="absolute -bottom-1 left-0 right-0 h-0.5 bg-primary rounded-full z-10"
        />
      )}
    </div>
  );
});

export { DraggableWebsiteCard }; 