'use client';

import { useEffect } from 'react';
import { useCategoriesStore } from '@/stores/categories-store';
import { useWebsitesStore } from '@/stores/websites-store';

interface DataProviderProps {
  children: React.ReactNode;
}

export function DataProvider({ children }: DataProviderProps) {
  const initializeCategories = useCategoriesStore((state) => state.initializeCategories);
  const initializeWebsites = useWebsitesStore((state) => state.initializeWebsites);

  useEffect(() => {
    // Initialize data stores when the component mounts
    initializeCategories();
    initializeWebsites();
  }, [initializeCategories, initializeWebsites]);

  return <>{children}</>;
} 