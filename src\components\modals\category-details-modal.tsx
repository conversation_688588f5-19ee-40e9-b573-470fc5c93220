'use client';

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Plus, ExternalLink, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Category, Website } from '@/types';
import { DraggableWebsiteCard } from '@/components/dashboard/draggable-website-card';
import { getFaviconFallbacks } from '@/utils';

interface CategoryDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  category: Category | null;
  websites: Website[];
  iconComponent: React.ComponentType<{ className?: string; style?: React.CSSProperties }>;
  onAddWebsite: (categoryId: string) => void;
  onOpenWebsite: (url: string) => void;
  onDeleteWebsite: (websiteId: string) => void;
  onEditWebsite?: (website: Website) => void;
  onMoveWebsite?: (websiteId: string, targetCategoryId: string) => void;
  onReorderWebsite?: (websiteId: string, targetWebsiteId: string, position: 'before' | 'after') => void;
  onMoveToPosition?: (websiteId: string, targetCategoryId: string, targetWebsiteId: string, position: 'before' | 'after') => void;
}

export function CategoryDetailsModal({
  isOpen,
  onClose,
  category,
  websites,
  iconComponent: IconComponent,
  onAddWebsite,
  onOpenWebsite,
  onDeleteWebsite,
  onEditWebsite,
  onMoveWebsite,
  onReorderWebsite,
  onMoveToPosition,
}: CategoryDetailsModalProps) {
  const [searchQuery, setSearchQuery] = useState('');

  // Filter websites based on search query
  const filteredWebsites = websites.filter(website =>
    website.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    website.url.toLowerCase().includes(searchQuery.toLowerCase()) ||
    website.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleBackdropClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  }, [onClose]);

  if (!isOpen || !category) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 flex items-center justify-center z-[70] p-4"
        onClick={handleBackdropClick}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="bg-background sketchy-box-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div 
            className="flex items-center justify-between p-6 border-b"
            style={{
              backgroundColor: `${category.color}12`,
              borderBottomColor: `${category.color}25`,
              background: `
                linear-gradient(135deg, 
                  ${category.color}20 0%, 
                  ${category.color}15 50%, 
                  ${category.color}12 100%
                )
              `,
            }}
          >
            <div className="flex items-center space-x-4">
              <div 
                className="p-3 sketchy-box-sm shadow-sm"
                style={{ 
                  backgroundColor: `${category.color}35`, 
                  border: `2px solid ${category.color}50`,
                  boxShadow: `
                    0 4px 8px ${category.color}15,
                    0 0 0 1px ${category.color}40,
                    inset 0 1px 0 ${category.color}20
                  `
                }}
              >
                <IconComponent 
                  className="h-8 w-8" 
                  style={{ color: category.color }}
                />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-foreground">{category.name}</h2>
                <p className="text-muted-foreground">
                  {websites.length} {websites.length === 1 ? 'website' : 'websites'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onAddWebsite(category.id)}
                className="flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span>Add Website</span>
              </Button>
              
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Search Bar */}
          <div className="p-4 border-b">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search websites..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-10"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"
                  title="Clear search"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>

          {/* Content */}
          <div 
            className="overflow-y-auto"
            style={{ maxHeight: 'calc(90vh - 200px)' }}
          >
            {filteredWebsites.length === 0 ? (
              <div className="text-center py-12">
                {searchQuery ? (
                  <div>
                    <p className="text-muted-foreground mb-4">
                      No websites found matching "{searchQuery}"
                    </p>
                    <Button variant="outline" onClick={() => setSearchQuery('')}>
                      Clear Search
                    </Button>
                  </div>
                ) : (
                  <div>
                    <p className="text-muted-foreground mb-4">
                      No websites in this category yet.
                    </p>
                    <Button onClick={() => onAddWebsite(category.id)}>
                      <Plus className="mr-2 h-4 w-4" />
                      Add First Website
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredWebsites.map((website) => (
                    <motion.div
                      key={website.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="group"
                    >
                      <Card className="h-full hover:shadow-lg transition-all duration-200 cursor-pointer">
                        <CardContent 
                          className="p-4"
                          onClick={() => onOpenWebsite(website.url)}
                        >
                          <div className="flex items-start space-x-3">
                            <div className="flex-shrink-0">
                              <img
                                src={website.faviconUrl || getFaviconFallbacks(website.url)[0]}
                                alt={`${website.name} favicon`}
                                className="w-8 h-8 rounded"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  const fallbacks = getFaviconFallbacks(website.url);
                                  const currentSrc = target.src;
                                  const currentIndex = fallbacks.indexOf(currentSrc);
                                  if (currentIndex < fallbacks.length - 1) {
                                    target.src = fallbacks[currentIndex + 1];
                                  }
                                }}
                              />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <h3 className="font-medium text-sm truncate pr-2">
                                  {website.name}
                                </h3>
                                <ExternalLink className="h-3 w-3 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0" />
                              </div>
                              {website.description && (
                                <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                                  {website.description}
                                </p>
                              )}
                              <p className="text-xs text-muted-foreground mt-1 truncate">
                                {new URL(website.url).hostname}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
} 