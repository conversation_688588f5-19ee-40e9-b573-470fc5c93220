@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    /* Main backgrounds using LlamaFeed dark theme colors */
    --background: 217 33% 12%;  /* #111827 - Main dark background */
    --foreground: 210 40% 98%;
    --card: 218 18% 21%;        /* #374151 - Category card background */
    --card-foreground: 210 40% 98%;
    --popover: 218 18% 21%;     /* #374151 - Popover background */
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 218 23% 11%;
    --secondary: 218 15% 26%;   /* #343d4d - Secondary elements */
    --secondary-foreground: 210 40% 98%;
    --muted: 218 15% 26%;       /* #343d4d - Muted backgrounds */
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 218 15% 26%;      /* #343d4d - Accent background */
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 218 15% 26%;      /* #343d4d - Border color */
    --input: 218 15% 26%;       /* #343d4d - Input background */
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Compact Masonry Grid Styles */
@layer components {
  .compact-masonry-grid {
    /* Fallback for browsers without masonry support */
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
    align-items: start;
  }

  /* For browsers with masonry support */
  @supports (grid-template-rows: masonry) {
    .compact-masonry-grid {
      grid-template-rows: masonry;
      grid-auto-flow: column dense;
    }
  }

  /* Enhanced compact layout using CSS columns as fallback */
  .compact-masonry-fallback {
    column-count: 1;
    column-gap: 1.5rem;
    column-fill: balance;
  }

  @media (min-width: 640px) {
    .compact-masonry-fallback {
      column-count: 2;
    }
  }

  @media (min-width: 1024px) {
    .compact-masonry-fallback {
      column-count: 3;
    }
  }

  @media (min-width: 1280px) {
    .compact-masonry-fallback {
      column-count: 4;
    }
  }

  @media (min-width: 1536px) {
    .compact-masonry-fallback {
      column-count: 5;
    }
  }

  .compact-masonry-fallback > * {
    break-inside: avoid;
    margin-bottom: 1.5rem;
    display: block;
  }

  /* Enhanced responsive grid with better spacing (fallback for skeleton) */
  .responsive-compact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(320px, 100%), 1fr));
    gap: 1.25rem;
    align-items: start;
    grid-auto-flow: row dense;
  }

  @media (min-width: 640px) {
    .responsive-compact-grid {
      gap: 1.5rem;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }
  }

  @media (min-width: 1024px) {
    .responsive-compact-grid {
      gap: 1.5rem;
      grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
    }
  }

  @media (min-width: 1280px) {
    .responsive-compact-grid {
      gap: 1.75rem;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
  }

  /* Masonry layout container */
  .masonry-container {
    position: relative;
    width: 100%;
  }

  /* Ensure consistent card heights and better visual flow */
  .category-card-container {
    width: 100%;
    break-inside: avoid;
    page-break-inside: avoid;
    -webkit-column-break-inside: avoid;
    will-change: transform;
  }

  /* Add subtle hover effect for better interactivity */
  .category-card-container:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease-out;
  }

  /* Ensure proper z-index stacking for dragged items */
  .category-card-container.z-50 {
    z-index: 50;
  }

  /* Better card spacing for short categories */
  .category-card-container[data-website-count="0"],
  .category-card-container[data-website-count="1"],
  .category-card-container[data-website-count="2"] {
    grid-row: span 1;
  }

  /* Optimize space for categories with many websites */
  .category-card-container[data-website-count="5"],
  .category-card-container[data-website-count="6"],
  .category-card-container[data-website-count="7"] {
    grid-row: auto;
  }

  /* Sketchy hand-drawn styles */
  .sketchy-box {
    border-radius: 15px 25px 12px 18px;
    border: 2px solid;
    border-color: inherit;
    position: relative;
  }

  .sketchy-box::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 18px 28px 15px 21px;
    border: 1px solid;
    border-color: inherit;
    opacity: 0.3;
    z-index: -1;
  }

  .sketchy-box-sm {
    border-radius: 8px 12px 6px 10px;
    border: 1.5px solid;
    border-color: inherit;
  }

  .sketchy-box-lg {
    border-radius: 20px 30px 16px 25px;
    border: 2.5px solid;
    border-color: inherit;
    position: relative;
  }

  .sketchy-box-lg::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 23px 33px 19px 28px;
    border: 1px solid;
    border-color: inherit;
    opacity: 0.2;
    z-index: -1;
  }
}
