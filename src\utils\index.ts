import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { PopupOptions, OpeningMode } from '@/types';
import { DEFAULT_POPUP_OPTIONS } from '@/constants';

/**
 * Merge class names with Tailwind CSS conflict resolution
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Generate a unique ID
 */
export function generateId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Validate URL format
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Normalize URL by adding protocol if missing
 */
export function normalizeUrl(url: string): string {
  if (!url) return '';
  
  // If URL doesn't start with protocol, add https://
  if (!/^https?:\/\//i.test(url)) {
    return `https://${url}`;
  }
  
  return url;
}

/**
 * Extract domain from URL
 */
export function extractDomain(url: string): string {
  try {
    const normalizedUrl = normalizeUrl(url);
    const urlObj = new URL(normalizedUrl);
    return urlObj.hostname;
  } catch {
    return '';
  }
}

/**
 * Generate favicon URL from domain with multiple fallback strategies
 */
export function generateFaviconUrl(url: string): string {
  const domain = extractDomain(url);
  if (!domain) return '';
  
  // Use a more reliable favicon service as the primary method
  return `https://icons.duckduckgo.com/ip3/${domain}.ico`;
}

/**
 * Get multiple favicon URL options for fallback
 */
export function getFaviconFallbacks(url: string): string[] {
  const domain = extractDomain(url);
  if (!domain) return [];
  
  return [
    // DuckDuckGo's favicon service (usually better quality)
    `https://icons.duckduckgo.com/ip3/${domain}.ico`,
    // Yandex favicon service (good alternative)
    `https://favicon.yandex.net/favicon/${domain}`,
    // Common favicon locations on the actual domain
    `https://${domain}/favicon.ico`,
    `https://${domain}/favicon.png`,
    `https://${domain}/apple-touch-icon.png`,
    `https://${domain}/apple-touch-icon-precomposed.png`,
    // Alternative paths
    `https://${domain}/images/favicon.ico`,
    `https://${domain}/assets/favicon.ico`,
    `https://${domain}/static/favicon.ico`,
    `https://${domain}/public/favicon.ico`,
    // Google's service as last resort (smaller size, sometimes generic)
    `https://www.google.com/s2/favicons?domain=${domain}&sz=32`,
  ];
}

/**
 * Open URL in popup window
 */
export function openInPopup(url: string, options: Partial<PopupOptions> = {}): Window | null {
  if (typeof window === 'undefined') {
    return null;
  }

  const finalOptions = { ...DEFAULT_POPUP_OPTIONS, ...options };
  
  const features = [
    `width=${finalOptions.width}`,
    `height=${finalOptions.height}`,
    `scrollbars=${finalOptions.scrollbars ? 'yes' : 'no'}`,
    `resizable=${finalOptions.resizable ? 'yes' : 'no'}`,
    `menubar=${finalOptions.menubar ? 'yes' : 'no'}`,
    `toolbar=${finalOptions.toolbar ? 'yes' : 'no'}`,
    'location=no',
    'status=no',
  ].join(',');

  try {
    return window.open(url, '_blank', features);
  } catch (error) {
    console.error('Error opening popup:', error);
    // Fallback to regular window.open
    return window.open(url, '_blank');
  }
}

/**
 * Enhanced website opener with multiple modes
 */
export function openWebsite(url: string, mode: OpeningMode = 'popup', popupOptions?: Partial<PopupOptions>): void {
  if (!isValidUrl(url)) {
    console.error('Invalid URL:', url);
    return;
  }

  const normalizedUrl = normalizeUrl(url);

  switch (mode) {
    case 'popup':
      const optimalSize = getOptimalPopupSize();
      openInPopup(normalizedUrl, { ...optimalSize, ...popupOptions });
      break;
    case 'new-tab':
      window.open(normalizedUrl, '_blank', 'noopener,noreferrer');
      break;
    case 'same-tab':
      window.location.href = normalizedUrl;
      break;
    case 'modal':
      // For now, fallback to new tab - modal implementation would require additional UI
      window.open(normalizedUrl, '_blank', 'noopener,noreferrer');
      break;
    default:
      window.open(normalizedUrl, '_blank', 'noopener,noreferrer');
  }
}

/**
 * Get optimal popup size based on screen dimensions
 */
export function getOptimalPopupSize(): { width: number; height: number } {
  if (!isBrowser()) return { width: 800, height: 600 };

  const screenWidth = window.screen.width;
  const screenHeight = window.screen.height;

  // Use 80% of screen size but with reasonable limits
  const width = Math.min(Math.max(screenWidth * 0.8, 600), 1200);
  const height = Math.min(Math.max(screenHeight * 0.8, 500), 900);

  return { width, height };
}

/**
 * Debounce function to limit function calls
 */
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function to limit function calls
 */
export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * Format date to readable string
 */
export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
}

/**
 * Get relative time string (e.g., "2 hours ago")
 */
export function getRelativeTime(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'just now';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) {
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  }

  return formatDate(date);
}

/**
 * Capitalize first letter of string
 */
export function capitalize(str: string): string {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Truncate string to specified length
 */
export function truncate(str: string, length: number): string {
  if (!str || str.length <= length) return str;
  return str.slice(0, length) + '...';
}

/**
 * Check if code is running in browser
 */
export function isBrowser(): boolean {
  return typeof window !== 'undefined';
}

/**
 * Get system theme preference
 */
export function getSystemTheme(): 'light' | 'dark' {
  if (!isBrowser()) return 'light';
  
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
}

/**
 * Copy text to clipboard
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  if (!isBrowser()) return false;

  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
} 