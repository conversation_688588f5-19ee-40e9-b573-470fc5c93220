'use client';

import { useState, useEffect } from 'react';
import { X, Globe, Shield, Monitor, Briefcase, GraduationCap, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useCategories } from '@/stores/categories-store';
import { Category } from '@/types';

interface AddCategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  editCategory?: Category;
}

const CATEGORY_COLORS = [
  '#3B82F6', // blue-500
  '#10B981', // emerald-500
  '#F59E0B', // amber-500
  '#8B5CF6', // violet-500
  '#EF4444', // red-500
  '#EC4899', // pink-500
  '#06B6D4', // cyan-500
  '#84CC16', // lime-500
  '#F97316', // orange-500
  '#A855F7', // purple-500 (different shade)
  '#6B7280', // gray-500
  '#DC2626', // red-600
];

const CATEGORY_ICONS = [
  { name: 'Globe', icon: Globe, label: 'Globe' },
  { name: 'Shield', icon: Shield, label: 'Shield' },
  { name: 'Monitor', icon: Monitor, label: 'Monitor' },
  { name: 'Briefcase', icon: Briefcase, label: 'Briefcase' },
  { name: 'GraduationCap', icon: GraduationCap, label: 'Education' },
  { name: 'Heart', icon: Heart, label: 'Heart' },
];

export function AddCategoryModal({ isOpen, onClose, editCategory }: AddCategoryModalProps) {
  const [name, setName] = useState('');
  const [selectedColor, setSelectedColor] = useState(CATEGORY_COLORS[0]);
  const [selectedIcon, setSelectedIcon] = useState('Globe');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { addCategory, updateCategory, error: categoryError, categories } = useCategories();
  
  const isEditMode = !!editCategory;

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      if (editCategory) {
        // Pre-populate form with existing category data
        setName(editCategory.name);
        setSelectedColor(editCategory.color);
        setSelectedIcon(editCategory.icon || 'Globe');
      } else {
        // Reset form for add mode
      setName('');
      setSelectedColor(CATEGORY_COLORS[0]);
      setSelectedIcon('Globe');
      }
      setError('');
      setIsSubmitting(false);
    }
  }, [isOpen, editCategory]);

  // Display category error from store
  useEffect(() => {
    if (categoryError) {
      setError(categoryError);
    }
  }, [categoryError]);

  if (!isOpen) return null;

  const handleSubmit = async () => {
    if (!name.trim()) {
      setError('Category name is required');
      return;
    }

    // Check for duplicate names (case-insensitive) - exclude current category when editing
    const nameExists = categories.some(
      cat => cat.id !== editCategory?.id && cat.name.toLowerCase() === name.trim().toLowerCase()
    );

    if (nameExists) {
      setError('A category with this name already exists');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      if (isEditMode && editCategory) {
        // Update existing category
        updateCategory(editCategory.id, {
          name: name.trim(),
          color: selectedColor,
          icon: selectedIcon,
        });
      } else {
        // Create new category
      addCategory({
        name: name.trim(),
        color: selectedColor,
        icon: selectedIcon,
        order: categories.length,
      });
      }

      // Close modal on success
      if (!categoryError) {
        onClose();
      }
    } catch {
      setError(isEditMode ? 'Failed to update category' : 'Failed to create category');
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedIconComponent = CATEGORY_ICONS.find(icon => icon.name === selectedIcon);

  return (
    <div 
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div 
        className="bg-background sketchy-box-lg shadow-lg w-full max-w-md"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold">
              {isEditMode ? 'Edit Category' : 'Add Category'}
            </h2>
            <p className="text-sm text-muted-foreground mt-1">
              {isEditMode 
                ? 'Update your category settings' 
                : 'Create a new category to organize your websites'
              }
            </p>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="p-4 bg-destructive/10 text-destructive text-sm border-b">
            {error}
          </div>
        )}

        {/* Form */}
        <div className="p-6 space-y-6">
          {/* Category Preview */}
          <div className="flex items-center justify-center">
            <div className="flex items-center space-x-3 p-4 sketchy-box bg-muted/30">
              <div 
                className="p-2 sketchy-box-sm"
                style={{ backgroundColor: `${selectedColor}20` }}
              >
                {selectedIconComponent && (
                  <selectedIconComponent.icon 
                    className="h-6 w-6" 
                    style={{ color: selectedColor }}
                  />
                )}
              </div>
              <span className="font-semibold text-lg">
                {name || 'Category Name'}
              </span>
            </div>
          </div>

          {/* Category Name */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Category Name</label>
            <Input
              placeholder="e.g., Work Tools, Entertainment"
              value={name}
              onChange={(e) => setName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSubmit();
                }
              }}
            />
          </div>

          {/* Color Selection */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Category Color</label>
            <div className="grid grid-cols-6 gap-2">
              {CATEGORY_COLORS.map((color) => (
                <button
                  key={color}
                  onClick={() => setSelectedColor(color)}
                  className={`w-8 h-8 border-2 transition-all ${
                    selectedColor === color
                      ? 'border-foreground scale-110'
                      : 'border-transparent hover:scale-105'
                  }`}
                  style={{ 
                    backgroundColor: color,
                    borderRadius: '8px 12px 6px 10px'
                  }}
                  title={`Select ${color}`}
                />
              ))}
            </div>
          </div>

          {/* Icon Selection */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Category Icon</label>
            <div className="grid grid-cols-3 gap-2">
              {CATEGORY_ICONS.map((iconData) => {
                const IconComponent = iconData.icon;
                return (
                  <button
                    key={iconData.name}
                    onClick={() => setSelectedIcon(iconData.name)}
                    className={`p-3 sketchy-box-sm border transition-colors text-center ${
                      selectedIcon === iconData.name
                        ? 'border-primary bg-primary/10 text-primary'
                        : 'border-border hover:bg-accent'
                    }`}
                  >
                    <IconComponent className="h-6 w-6 mx-auto mb-1" />
                    <span className="text-sm">{iconData.label}</span>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-3 pt-4">
            <Button variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || !name.trim()}
              className="flex-1"
            >
              {isSubmitting 
                ? (isEditMode ? 'Updating...' : 'Creating...') 
                : (isEditMode ? 'Update Category' : 'Create Category')
              }
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
} 