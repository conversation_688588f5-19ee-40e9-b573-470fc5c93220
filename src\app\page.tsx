'use client';

import { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import { flushSync } from 'react-dom';
import { Header } from '@/components/layout/header';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, Globe, Shield, Monitor, Briefcase, GraduationCap, Heart } from 'lucide-react';
import { useCategories } from '@/stores/categories-store';
import { useWebsites } from '@/stores/websites-store';
import { useSearch } from '@/stores/search-store';
import { openWebsite } from '@/utils';
import { Category, Website, SearchSuggestion, NewWebsiteSuggestion, ExistingWebsiteSuggestion } from '@/types';
import { AddWebsiteModal } from '@/components/modals/add-website-modal';
import { AddCategoryModal } from '@/components/modals/add-category-modal';
import { EditWebsiteModal } from '@/components/modals/edit-website-modal';
import { CategoryDetailsModal } from '@/components/modals/category-details-modal';

import { AnimatedCategoryCard } from '@/components/dashboard/animated-category-card';
import { DragDropProvider } from '@/components/dashboard/drag-drop-provider';
import { DndCategoryGrid } from '@/components/dashboard/dnd-category-grid';
import { CategoryCardSkeleton } from '@/components/dashboard/category-card-skeleton';

export default function Home() {
  const { categories, reorderCategories, deleteCategory, isLoading: categoriesLoading } = useCategories();
  const { 
    getWebsitesByCategory, 
    deleteWebsite, 
    deleteWebsitesByCategory, 
    moveWebsite, 
    reorderWebsites, 
    isLoading: websitesLoading,
    websites: allWebsites
  } = useWebsites();
  const { 
    query, 
    suggestions, 
    showSuggestions, 
    setSearchQuery, 
    clearSearch, 
    updateSuggestions,
    performSearch,
    setShowSuggestions
  } = useSearch();

  // Modal states
  const [isAddWebsiteModalOpen, setIsAddWebsiteModalOpen] = useState(false);
  const [isAddCategoryModalOpen, setIsAddCategoryModalOpen] = useState(false);
  const [isEditWebsiteModalOpen, setIsEditWebsiteModalOpen] = useState(false);
  const [isCategoryDetailsModalOpen, setIsCategoryDetailsModalOpen] = useState(false);
  const [selectedCategoryForWebsite, setSelectedCategoryForWebsite] = useState<string>('');
  const [editingCategory, setEditingCategory] = useState<Category | undefined>(undefined);
  const [editingWebsite, setEditingWebsite] = useState<Website | null>(null);
  const [selectedCategoryForDetails, setSelectedCategoryForDetails] = useState<Category | null>(null);
  const [prefilledWebsiteData, setPrefilledWebsiteData] = useState<Partial<Website> | null>(null);
  const [searchLoading, setSearchLoading] = useState(false);

  // Debounced search effect
  useEffect(() => {
    if (!query.trim()) {
      updateSuggestions(categories, allWebsites, '');
      return;
    }

    setSearchLoading(true);
    const debounceTimer = setTimeout(() => {
      updateSuggestions(categories, allWebsites, query);
      setSearchLoading(false);
    }, 300);

    return () => {
      clearTimeout(debounceTimer);
      setSearchLoading(false);
    };
  }, [query, categories, allWebsites, updateSuggestions]);

  // Filter categories and websites based on search
  const filteredContent = useMemo(() => {
    if (!query.trim()) {
      return {
        categories,
        showCategories: categories,
        hasSearchResults: false
      };
    }

    const results = performSearch(categories, allWebsites, query);
    
    // If we have search results, show only matching categories with their websites
    if (results.hasResults) {
      return {
        categories: results.categories,
        showCategories: results.categories,
        hasSearchResults: true
      };
    }

    // No results found
    return {
      categories: [],
      showCategories: [],
      hasSearchResults: true
    };
  }, [query, categories, allWebsites, performSearch]);

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    if (!value.trim()) {
      setShowSuggestions(false);
    }
  };

  const handleSearchClear = () => {
    clearSearch();
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    if (suggestion.type === 'existing') {
      // Navigate to the website (open it)
      handleOpenWebsite(suggestion.website.url);
      // Clear search to show the category
      clearSearch();
    } else {
      // For new website suggestions, open the website in a new tab
      openWebsite(suggestion.url, 'new-tab');
    }
    setShowSuggestions(false);
  };

  const handleAddWebsiteFromSuggestion = (suggestion: NewWebsiteSuggestion) => {
    console.log('🔧 Adding website from suggestion:', suggestion.name);
    
    // Use flushSync to ensure all state updates happen immediately
    flushSync(() => {
      // Clear search and hide suggestions immediately
      setSearchQuery('');
      setShowSuggestions(false);
      
      // Pre-fill the add website modal with suggestion data
      setPrefilledWebsiteData({
        name: suggestion.name,
        url: suggestion.url,
        description: suggestion.description,
        faviconUrl: suggestion.faviconUrl
      });
      
      setSelectedCategoryForWebsite(''); // Let user choose category
      setIsAddWebsiteModalOpen(true);
    });
    
    console.log('🔧 Modal should be open now');
  };

  const handleAddExistingWebsiteToCategory = (suggestion: ExistingWebsiteSuggestion) => {
    console.log('🔧 Adding existing website to new category:', suggestion.website.name);
    
    // Use flushSync to ensure all state updates happen immediately
    flushSync(() => {
      // Clear search and hide suggestions immediately
      setSearchQuery('');
      setShowSuggestions(false);
      
      // Pre-fill the add website modal with existing website data
      setPrefilledWebsiteData({
        name: suggestion.website.name,
        url: suggestion.website.url,
        description: suggestion.website.description,
        faviconUrl: suggestion.website.faviconUrl
      });
      
      setSelectedCategoryForWebsite(''); // Let user choose category
      setIsAddWebsiteModalOpen(true);
    });
    
    console.log('🔧 Modal should be open now for existing website');
  };

  const handleOpenWebsite = (url: string) => {
    openWebsite(url, 'popup');
  };

  const handleAddWebsiteClick = (categoryId?: string) => {
    setSelectedCategoryForWebsite(categoryId || '');
    setPrefilledWebsiteData(null); // Clear any prefilled data
    setIsAddWebsiteModalOpen(true);
  };

  const handleCloseAddWebsiteModal = () => {
    setIsAddWebsiteModalOpen(false);
    setPrefilledWebsiteData(null);
  };

  const handleDeleteWebsite = (websiteId: string) => {
    if (confirm('Are you sure you want to delete this website?')) {
      deleteWebsite(websiteId);
    }
  };

  const handleEditWebsite = (website: Website) => {
    console.log('📝 Edit website:', website.name);
    setEditingWebsite(website);
    setIsEditWebsiteModalOpen(true);
  };

  const handleCloseEditWebsiteModal = () => {
    setIsEditWebsiteModalOpen(false);
    setEditingWebsite(null);
  };

  const handleMoveWebsite = (websiteId: string, targetCategoryId: string) => {
    console.log('🎯 handleMoveWebsite called:', websiteId, '->', targetCategoryId);
    moveWebsite(websiteId, targetCategoryId);
  };

  const handleReorderWebsite = (websiteId: string, targetWebsiteId: string, position: 'before' | 'after') => {
    console.log('🔄 handleReorderWebsite called:', websiteId, position, targetWebsiteId);
    
    // Find the websites involved
    const allWebsites = categories.flatMap(cat => getWebsitesByCategory(cat.id));
    const draggedWebsite = allWebsites.find(w => w.id === websiteId);
    const targetWebsite = allWebsites.find(w => w.id === targetWebsiteId);
    
    console.log('   Dragged website:', draggedWebsite?.name, '(category:', draggedWebsite?.categoryId, ')');
    console.log('   Target website:', targetWebsite?.name, '(category:', targetWebsite?.categoryId, ')');
    
    if (!draggedWebsite || !targetWebsite || draggedWebsite.categoryId !== targetWebsite.categoryId) {
      console.log('❌ Invalid reorder: websites not in same category');
      return;
    }
    
    // Get all websites in the category, sorted by order
    const categoryWebsites = getWebsitesByCategory(draggedWebsite.categoryId);
    console.log('   Category websites before reorder:', categoryWebsites.map(w => `${w.name}(${w.order})`));
    
    // Remove the dragged website from its current position
    const filteredWebsites = categoryWebsites.filter(w => w.id !== websiteId);
    console.log('   Filtered websites:', filteredWebsites.map(w => w.name));
    
    // Find the target index
    const targetIndex = filteredWebsites.findIndex(w => w.id === targetWebsiteId);
    let insertIndex = targetIndex;
    
    if (position === 'after') {
      insertIndex = targetIndex + 1;
    }
    
    console.log('   Target index:', targetIndex, 'Insert index:', insertIndex);
    
    // Insert the dragged website at the new position
    const reorderedWebsites = [
      ...filteredWebsites.slice(0, insertIndex),
      draggedWebsite,
      ...filteredWebsites.slice(insertIndex)
    ];
    
    console.log('📝 Reordered websites:', reorderedWebsites.map(w => w.name));
    
    // Call the store's reorder function
    reorderWebsites(draggedWebsite.categoryId, reorderedWebsites);
    console.log('✅ Called reorderWebsites in store');
  };

  const handleMoveToPosition = (websiteId: string, targetCategoryId: string, targetWebsiteId: string, position: 'before' | 'after') => {
    console.log('🎯 handleMoveToPosition called:', websiteId, '->', targetCategoryId, 'at position:', position, 'relative to:', targetWebsiteId);
    
    // Find the websites involved
    const allWebsites = categories.flatMap(cat => getWebsitesByCategory(cat.id));
    const draggedWebsite = allWebsites.find(w => w.id === websiteId);
    const targetWebsite = allWebsites.find(w => w.id === targetWebsiteId);
    
    if (!draggedWebsite || !targetWebsite) {
      console.log('❌ Could not find dragged or target website');
      return;
    }
    
    console.log('   Dragged website:', draggedWebsite.name, 'from category:', draggedWebsite.categoryId);
    console.log('   Target website:', targetWebsite.name, 'in category:', targetWebsite.categoryId);
    
    // First, move the website to the target category
    moveWebsite(websiteId, targetCategoryId);
    
    // Then reorder it within the target category
    // We need to wait a tick for the store to update, then reorder
    setTimeout(() => {
      const targetCategoryWebsites = getWebsitesByCategory(targetCategoryId);
      console.log('   Target category websites after move:', targetCategoryWebsites.map(w => w.name));
      
      // Find the websites in the target category
      const movedWebsite = targetCategoryWebsites.find(w => w.id === websiteId);
      const referenceWebsite = targetCategoryWebsites.find(w => w.id === targetWebsiteId);
      
      if (!movedWebsite || !referenceWebsite) {
        console.log('❌ Could not find websites after move');
        return;
      }
      
      // Remove the moved website from its current position in the target category
      const filteredWebsites = targetCategoryWebsites.filter(w => w.id !== websiteId);
      
      // Find the reference website index in the filtered list
      const referenceIndex = filteredWebsites.findIndex(w => w.id === targetWebsiteId);
      let insertIndex = referenceIndex;
      
      if (position === 'after') {
        insertIndex = referenceIndex + 1;
      }
      
      console.log('   Reference index:', referenceIndex, 'Insert index:', insertIndex);
      
      // Insert the moved website at the desired position
      const reorderedWebsites = [
        ...filteredWebsites.slice(0, insertIndex),
        movedWebsite,
        ...filteredWebsites.slice(insertIndex)
      ];
      
      console.log('📝 Final reordered websites:', reorderedWebsites.map(w => w.name));
      
      // Update the order in the store
      reorderWebsites(targetCategoryId, reorderedWebsites);
      console.log('✅ Cross-category positioning complete');
    }, 50); // Small delay to allow store update
  };

  const handleReorderCategories = (reorderedCategories: Category[]) => {
    console.log('🔄 handleReorderCategories called with', reorderedCategories.length, 'categories');
    console.log('   - New order:', reorderedCategories.map((c, i) => `${i}: ${c.name}`));
    
    // Call the store's reorder function
    reorderCategories(reorderedCategories);
    console.log('✅ reorderCategories completed');
  };

  const handleEditCategory = (category: Category) => {
    console.log('📝 Edit category:', category.name);
    setEditingCategory(category);
    setIsAddCategoryModalOpen(true);
  };

  const handleCloseAddCategoryModal = () => {
    setIsAddCategoryModalOpen(false);
    setEditingCategory(undefined);
  };

  const handleDeleteCategory = (categoryId: string) => {
    console.log('🗑️ Delete category:', categoryId);
    // Add confirmation dialog and delete logic
    if (confirm('Are you sure you want to delete this category? All websites in this category will also be deleted.')) {
      // First delete all websites in the category
      deleteWebsitesByCategory(categoryId);
      // Then delete the category itself
      deleteCategory(categoryId);
    }
  };

  const handleOpenCategoryDetails = (category: Category) => {
    console.log('📂 Opening category details:', category.name);
    setSelectedCategoryForDetails(category);
    setIsCategoryDetailsModalOpen(true);
  };

  const handleCloseCategoryDetailsModal = () => {
    setIsCategoryDetailsModalOpen(false);
    setSelectedCategoryForDetails(null);
  };

  // Icon mapping for categories
  const iconMap = {
    Globe,
    Shield,
    Monitor,
    Briefcase,
    GraduationCap,
    Heart,
  };

  // Show loading state with skeleton screens
  if (categoriesLoading || websitesLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Header 
          searchValue={query}
          onSearchChange={handleSearchChange}
          onSearchClear={handleSearchClear}
          suggestions={suggestions}
          showSuggestions={showSuggestions}
          searchLoading={searchLoading}
          onSuggestionClick={handleSuggestionClick}
          onAddWebsite={handleAddWebsiteFromSuggestion}
          onAddExistingWebsite={handleAddExistingWebsiteToCategory}
        />
        <main className="container max-w-screen-2xl mx-auto px-4 py-8">
          <div className="text-center mb-8">
            <motion.h1 
              className="text-4xl font-bold text-foreground mb-4"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              Welcome to StackZest
            </motion.h1>
            <motion.p 
              className="text-xl text-muted-foreground"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              Organize your tech stack and stay up-to-date with the latest technologies.
            </motion.p>
          </div>

          {/* Skeleton Grid */}
          <div className="responsive-compact-grid">
            {Array.from({ length: 6 }).map((_, index) => (
              <CategoryCardSkeleton key={index} />
            ))}
          </div>
          <div className="text-center mt-8">
            <p className="text-muted-foreground">Loading your dashboard...</p>
          </div>
        </main>
      </div>
    );
  }

  // Show no results state when searching but no matches found
  if (filteredContent.hasSearchResults && filteredContent.showCategories.length === 0) {
    return (
      <DragDropProvider>
        <div className="min-h-screen bg-background">
          <Header 
            searchValue={query}
            onSearchChange={handleSearchChange}
            onSearchClear={handleSearchClear}
            suggestions={suggestions}
            showSuggestions={showSuggestions}
            searchLoading={searchLoading}
            onSuggestionClick={handleSuggestionClick}
            onAddWebsite={handleAddWebsiteFromSuggestion}
            onAddExistingWebsite={handleAddExistingWebsiteToCategory}
          />
          
          <main className="container max-w-screen-2xl mx-auto px-4 py-8">
            <div className="text-center py-12">
              <div className="mx-auto max-w-md">
                <div className="mb-4">
                  <Globe className="mx-auto h-12 w-12 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">
                  No websites found
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  No websites match your search &quot;{query}&quot;. Try a different search term.
                </p>
                <Button 
                  variant="outline" 
                  onClick={handleSearchClear}
                >
                  Clear Search
                </Button>
              </div>
            </div>
          </main>
        </div>
      </DragDropProvider>
    );
  }

  return (
    <DragDropProvider>
      <div className="min-h-screen bg-background">
      <Header 
        searchValue={query}
        onSearchChange={handleSearchChange}
        onSearchClear={handleSearchClear}
        suggestions={suggestions}
        showSuggestions={showSuggestions}
        searchLoading={searchLoading}
        onSuggestionClick={handleSuggestionClick}
        onAddWebsite={handleAddWebsiteFromSuggestion}
        onAddExistingWebsite={handleAddExistingWebsiteToCategory}
      />
      
      <main className="container max-w-screen-2xl mx-auto px-4 py-8">
        {/* Welcome section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold tracking-tight mb-2">
            {query ? `Search results for "${query}"` : 'Welcome to StackZest'}
          </h2>
          <p className="text-muted-foreground text-lg">
            {query 
              ? `Found ${filteredContent.showCategories.length} ${filteredContent.showCategories.length === 1 ? 'category' : 'categories'} matching your search`
              : 'Organize your tech stack and stay up-to-date with the latest technologies.'
            }
          </p>
        </div>

        {/* Categories grid */}
        <DndCategoryGrid
          categories={filteredContent.showCategories}
          onReorderCategories={handleReorderCategories}
          getWebsiteCount={(categoryId) => {
            const categoryWebsites = getWebsitesByCategory(categoryId);
            // If searching, only count matching websites
            if (query) {
              const searchTerm = query.toLowerCase();
              const isSingleChar = searchTerm.length === 1;
              
              if (isSingleChar) {
                // For single character searches, only show websites that START with that character
                return categoryWebsites.filter(website => 
                  website.name.toLowerCase().startsWith(searchTerm)
                ).length;
              } else {
                // For multi-character searches, include partial matches
                return categoryWebsites.filter(website => 
                  website.name.toLowerCase().includes(searchTerm) ||
                  website.url.toLowerCase().includes(searchTerm) ||
                  website.description?.toLowerCase().includes(searchTerm)
                ).length;
              }
            }
            return categoryWebsites.length;
          }}
          onEditCategory={handleEditCategory}
          onDeleteCategory={handleDeleteCategory}
        >
          {(category: Category) => {
            const IconComponent = iconMap[category.icon as keyof typeof iconMap] || Globe;
            let categoryWebsites = getWebsitesByCategory(category.id);
            
            // Filter websites if searching
            if (query) {
              const searchTerm = query.toLowerCase();
              const isSingleChar = searchTerm.length === 1;
              
              if (isSingleChar) {
                // For single character searches, only show websites that START with that character
                categoryWebsites = categoryWebsites.filter(website => 
                  website.name.toLowerCase().startsWith(searchTerm)
                );
              } else {
                // For multi-character searches, include partial matches
                categoryWebsites = categoryWebsites.filter(website => 
                  website.name.toLowerCase().includes(searchTerm) ||
                  website.url.toLowerCase().includes(searchTerm) ||
                  website.description?.toLowerCase().includes(searchTerm)
                );
              }
            }
            
            return (
              <AnimatedCategoryCard
                category={category}
                websites={categoryWebsites}
                iconComponent={IconComponent}
                onAddWebsite={handleAddWebsiteClick}
                onOpenWebsite={handleOpenWebsite}
                onDeleteWebsite={handleDeleteWebsite}
                onEditWebsite={handleEditWebsite}
                onMoveWebsite={handleMoveWebsite}
                onReorderWebsite={handleReorderWebsite}
                onMoveToPosition={handleMoveToPosition}
                onCardClick={handleOpenCategoryDetails}
              />
            );
          }}
        </DndCategoryGrid>

        {/* Add Category button - only show when not searching */}
        {!query && (
          <div className="mt-8 flex justify-center">
            <Button 
              variant="outline" 
              size="lg"
              onClick={() => setIsAddCategoryModalOpen(true)}
            >
              <Plus className="mr-2 h-5 w-5" />
              Add Category
            </Button>
          </div>
        )}

        {/* Quick stats or tips section - only show when not searching */}
        {!query && (
          <div className="mt-12 text-center">
            <Card className="max-w-2xl mx-auto">
              <CardHeader>
                <CardTitle>Getting Started</CardTitle>
                <CardDescription>
                  Start organizing your digital life by adding websites to categories
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="flex flex-col items-center space-y-2">
                    <div className="p-2 bg-primary/10 rounded-full">
                      <Plus className="h-4 w-4 text-primary" />
                    </div>
                    <span>Add your favorite websites</span>
                  </div>
                  <div className="flex flex-col items-center space-y-2">
                    <div className="p-2 bg-primary/10 rounded-full">
                      <Globe className="h-4 w-4 text-primary" />
                    </div>
                    <span>Organize by categories</span>
                  </div>
                  <div className="flex flex-col items-center space-y-2">
                    <div className="p-2 bg-primary/10 rounded-full">
                      <Monitor className="h-4 w-4 text-primary" />
                    </div>
                    <span>Access with one click</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </main>

      {/* Modals */}
      <AddWebsiteModal
        isOpen={isAddWebsiteModalOpen}
        onClose={handleCloseAddWebsiteModal}
        categoryId={selectedCategoryForWebsite}
        prefilledData={prefilledWebsiteData}
      />
      
      <AddCategoryModal
        isOpen={isAddCategoryModalOpen}
        onClose={handleCloseAddCategoryModal}
        editCategory={editingCategory}
      />

      <EditWebsiteModal
        isOpen={isEditWebsiteModalOpen}
        onClose={handleCloseEditWebsiteModal}
        website={editingWebsite}
      />

      <CategoryDetailsModal
        isOpen={isCategoryDetailsModalOpen}
        onClose={handleCloseCategoryDetailsModal}
        category={selectedCategoryForDetails}
        websites={selectedCategoryForDetails ? getWebsitesByCategory(selectedCategoryForDetails.id) : []}
        iconComponent={selectedCategoryForDetails ? iconMap[selectedCategoryForDetails.icon as keyof typeof iconMap] || Globe : Globe}
        onAddWebsite={handleAddWebsiteClick}
        onOpenWebsite={handleOpenWebsite}
        onDeleteWebsite={handleDeleteWebsite}
        onEditWebsite={handleEditWebsite}
        onMoveWebsite={handleMoveWebsite}
        onReorderWebsite={handleReorderWebsite}
        onMoveToPosition={handleMoveToPosition}
      />

    </div>
    </DragDropProvider>
  );
}
