'use client';

import React, { useState, useCallback, memo } from 'react';
import { MoreHorizontal, Edit, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Category } from '@/types';

interface CategoryActionsMenuProps {
  category: Category;
  onEdit?: (category: Category) => void;
  onDelete?: (categoryId: string) => void;
}

const CategoryActionsMenu = memo(function CategoryActionsMenu({
  category,
  onEdit,
  onDelete,
}: CategoryActionsMenuProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleAction = useCallback((action: () => void) => {
    action();
    setIsOpen(false);
  }, []);

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 opacity-30 group-hover:opacity-90 transition-opacity hover:bg-background/60"
          onClick={(e) => {
            e.stopPropagation(); // Prevent triggering parent click
          }}
          title="Category options"
        >
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {onEdit && (
          <DropdownMenuItem
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleAction(() => onEdit(category));
            }}
            className="flex items-center space-x-2"
          >
            <Edit className="h-4 w-4" />
            <span>Edit Category</span>
          </DropdownMenuItem>
        )}
        
        {onEdit && onDelete && <DropdownMenuSeparator />}
        
        {onDelete && (
          <DropdownMenuItem
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleAction(() => onDelete(category.id));
            }}
            className="flex items-center space-x-2 text-destructive focus:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
            <span>Delete Category</span>
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
});

export { CategoryActionsMenu }; 