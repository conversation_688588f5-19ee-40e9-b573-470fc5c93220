'use client';import React, { useState, useCallback, memo } from 'react';import { MoreHorizontal, ExternalLink, Edit, Trash2, Move } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Website } from '@/types';

interface WebsiteActionsMenuProps {
  website: Website;
  onOpen?: (url: string) => void;
  onEdit?: (website: Website) => void;
  onDelete?: (websiteId: string) => void;
  onMove?: (website: Website) => void;
}

const WebsiteActionsMenu = memo(function WebsiteActionsMenu({  website,  onOpen,  onEdit,  onDelete,  onMove,}: WebsiteActionsMenuProps) {  const [isOpen, setIsOpen] = useState(false);  const handleAction = useCallback((action: () => void) => {    action();    setIsOpen(false);  }, []);

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
          onClick={(e) => {
            e.stopPropagation(); // Prevent triggering parent click
          }}
        >
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
                {onOpen && (          <DropdownMenuItem            onClick={(e) => {              e.preventDefault();              e.stopPropagation();              handleAction(() => onOpen(website.url));            }}            className="flex items-center space-x-2"          >            <ExternalLink className="h-4 w-4" />            <span>Open Website</span>          </DropdownMenuItem>        )}
        
                {onEdit && (          <DropdownMenuItem            onClick={(e) => {              e.preventDefault();              e.stopPropagation();              handleAction(() => onEdit(website));            }}            className="flex items-center space-x-2"          >            <Edit className="h-4 w-4" />            <span>Edit</span>          </DropdownMenuItem>        )}
        
                {onMove && (          <DropdownMenuItem            onClick={(e) => {              e.preventDefault();              e.stopPropagation();              handleAction(() => onMove(website));            }}            className="flex items-center space-x-2"          >            <Move className="h-4 w-4" />            <span>Move to Category</span>          </DropdownMenuItem>        )}
        
        {(onOpen || onEdit || onMove) && onDelete && <DropdownMenuSeparator />}
        
                {onDelete && (          <DropdownMenuItem            onClick={(e) => {              e.preventDefault();              e.stopPropagation();              handleAction(() => onDelete(website.id));            }}            className="flex items-center space-x-2 text-destructive focus:text-destructive"          >            <Trash2 className="h-4 w-4" />            <span>Delete</span>          </DropdownMenuItem>        )}
            </DropdownMenuContent>    </DropdownMenu>  );});export { WebsiteActionsMenu }; 