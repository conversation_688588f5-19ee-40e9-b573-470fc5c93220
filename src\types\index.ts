// Core data types for StackZest application

export interface Website {
  id: string;
  name: string;
  url: string;
  faviconUrl?: string;
  description?: string;
  categoryId: string;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Category {
  id: string;
  name: string;
  color: string;
  icon?: string;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

// UI and interaction types
export interface DragItem {
  id: string;
  type: 'website' | 'category';
  sourceIndex: number;
  sourceCategoryId?: string;
}

export interface PopupOptions {  width: number;  height: number;  scrollbars: boolean;  resizable: boolean;  menubar: boolean;  toolbar: boolean;}export type OpeningMode = 'popup' | 'new-tab' | 'same-tab' | 'modal';

// Theme types
export type Theme = 'light' | 'dark' | 'system';

export interface ThemeState {
  theme: Theme;
  systemTheme: 'light' | 'dark';
  resolvedTheme: 'light' | 'dark';
}

// Store state types
export interface WebsiteState {
  websites: Website[];
  isLoading: boolean;
  error: string | null;
}

export interface CategoryState {
  categories: Category[];
  isLoading: boolean;
  error: string | null;
}

export interface SearchState {
  query: string;
  selectedCategory: string | null;
}

// Search suggestion types
export interface ExistingWebsiteSuggestion {
  type: 'existing';
  website: Website;
  category: Category;
  matchScore: number;
}

export interface NewWebsiteSuggestion {
  type: 'new';
  name: string;
  url: string;
  faviconUrl?: string;
  description?: string;
  category?: string;
  tags?: string[];
}

export type SearchSuggestion = ExistingWebsiteSuggestion | NewWebsiteSuggestion;

export interface SearchResults {
  categories: Category[];
  websites: Website[];
  suggestions: SearchSuggestion[];
  hasResults: boolean;
}

export interface EnhancedSearchState {
  query: string;
  selectedCategory: string | null;
  suggestions: SearchSuggestion[];
  showSuggestions: boolean;
  isSearching: boolean;
  searchResults: SearchResults;
}

// Component props types
export interface WebsiteCardProps {
  website: Website;
  onEdit?: (website: Website) => void;
  onRemove?: (websiteId: string) => void;
  onOpen?: (url: string) => void;
  isDragging?: boolean;
}

export interface CategoryContainerProps {
  category: Category;
  websites: Website[];
  onAddWebsite?: (categoryId: string) => void;
  onEditCategory?: (category: Category) => void;
  onRemoveCategory?: (categoryId: string) => void;
}

// Modal and form types
export interface AddWebsiteModalProps {
  isOpen: boolean;
  onClose: () => void;
  categoryId?: string;
  onSubmit: (websiteData: Partial<Website>) => void;
}

export interface AddCategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (categoryData: Partial<Category>) => void;
}

// Search and filter types (legacy - using new SearchState above)

// Local storage types
export interface StorageData {
  websites: Website[];
  categories: Category[];
  theme: Theme;
  version: string;
}

// Predefined website suggestions
export interface PreDefinedWebsite {
  name: string;
  url: string;
  category: string;
  description?: string;
  faviconUrl?: string;
}

// Export utility types
export type WithoutId<T> = Omit<T, 'id'>;
export type WithoutTimestamps<T> = Omit<T, 'createdAt' | 'updatedAt'>;
export type CreateInput<T> = WithoutId<WithoutTimestamps<T>>; 