'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  MeasuringStrategy,
  defaultDropAnimationSideEffects,
} from '@dnd-kit/core';
import {  arrayMove,  SortableContext,  sortableKeyboardCoordinates,  rectSortingStrategy,} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Category } from '@/types';
import { GripVertical } from 'lucide-react';
import { CategoryDragProvider } from './animated-category-card';
import { CategoryActionsMenu } from '@/components/ui/category-actions-menu';

interface SortableCategoryProps {  
  category: Category;  
  children: React.ReactNode;  
  websiteCount?: number;
  style?: React.CSSProperties;
  onEditCategory?: (category: Category) => void;
  onDeleteCategory?: (categoryId: string) => void;
}

function SortableCategory({ 
  category, 
  children, 
  websiteCount = 0, 
  style,
  onEditCategory,
  onDeleteCategory 
}: SortableCategoryProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: category.id });

  const dragStyle = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 999 : 1,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={{ ...style, ...dragStyle }}
      className={`group relative category-card-container ${isDragging ? 'z-50' : ''}`}
      data-website-count={websiteCount}
    >
      {/* Draggable name and icon area only - positioned over the left part of header */}
      <div
        {...attributes}
        {...listeners}
        className="absolute top-2 left-2 z-10 cursor-grab active:cursor-grabbing rounded-lg"
        title="Drag to reorder categories"
        style={{
          // Cover approximately where the icon and name are located
          width: '200px', // Adjust based on typical name + icon width
          height: '52px', // Height to cover icon and name area
          backgroundColor: 'transparent',
        }}
      />

      {/* Category actions and drag handle container */}
      <div className="absolute top-2 right-14 z-20 flex items-center space-x-3">
        {/* Category Actions Menu */}
        <CategoryActionsMenu
          category={category}
          onEdit={onEditCategory}
          onDelete={onDeleteCategory}
        />
        
        {/* Drag handle (also functional for dragging) */}
        <div
          {...attributes}
          {...listeners}
          className="p-1 rounded-md bg-background/80 backdrop-blur-sm border border-border/50 text-muted-foreground hover:text-foreground transition-all duration-200 opacity-30 group-hover:opacity-90 cursor-grab active:cursor-grabbing"
          title="Drag to reorder categories"
        >
          <GripVertical className="h-5 w-5" />
        </div>
      </div>

      {/* Category content */}
      {children}
    </div>
  );
}

interface DndCategoryGridProps {  
  categories: Category[];  
  onReorderCategories: (categories: Category[]) => void;  
  children: (category: Category, index: number) => React.ReactNode;  
  getWebsiteCount?: (categoryId: string) => number;
  onEditCategory?: (category: Category) => void;
  onDeleteCategory?: (categoryId: string) => void;
}

interface CategoryPosition {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
}

export function DndCategoryGrid({  
  categories,  
  onReorderCategories,  
  children,  
  getWebsiteCount,
  onEditCategory,
  onDeleteCategory,
}: DndCategoryGridProps) {
  const [activeId, setActiveId] = useState<string | null>(null);
  const [positions, setPositions] = useState<CategoryPosition[]>([]);
  const [containerHeight, setContainerHeight] = useState<number>(0);
  const gridRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<Map<string, HTMLDivElement>>(new Map());
  
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const measuring = {
    droppable: {
      strategy: MeasuringStrategy.Always,
    },
  };

  // Calculate masonry layout positions
  const calculateMasonryLayout = useCallback(() => {
    if (!gridRef.current || categories.length === 0) return;

    const container = gridRef.current;
    const containerWidth = container.offsetWidth;
    const gap = 24; // 1.5rem
    const minCardWidth = 320;
    
    // Calculate number of columns
    const maxColumns = Math.floor((containerWidth + gap) / (minCardWidth + gap));
    const columns = Math.min(Math.max(1, maxColumns), categories.length);
    const cardWidth = (containerWidth - (gap * (columns - 1))) / columns;
    
    // Initialize column heights
    const columnHeights = new Array(columns).fill(0);
    const newPositions: CategoryPosition[] = [];
    
    // Process each category
    categories.forEach((category) => {
      const element = itemRefs.current.get(category.id);
      if (!element) return;
      
      // Find the shortest column
      const shortestColumnIndex = columnHeights.indexOf(Math.min(...columnHeights));
      
      // Calculate position
      const x = shortestColumnIndex * (cardWidth + gap);
      const y = columnHeights[shortestColumnIndex];
      
      // Get the actual height of the element
      const height = element.offsetHeight || 300; // fallback height
      
      newPositions.push({
        id: category.id,
        x,
        y,
        width: cardWidth,
        height,
      });
      
      // Update column height
      columnHeights[shortestColumnIndex] += height + gap;
    });
    
    setPositions(newPositions);
    setContainerHeight(Math.max(...columnHeights) - gap);
  }, [categories]);

  // Measure and layout on mount and category changes
  useEffect(() => {
    // First render with basic layout
    if (positions.length === 0 && categories.length > 0) {
      setTimeout(() => {
        calculateMasonryLayout();
      }, 100);
    }
  }, [categories, calculateMasonryLayout, positions.length]);

  // Recalculate on resize
  useEffect(() => {
    const handleResize = () => {
      calculateMasonryLayout();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [calculateMasonryLayout]);

  // Recalculate when container content changes
  useEffect(() => {
    const observer = new MutationObserver(() => {
      setTimeout(calculateMasonryLayout, 50);
    });

    if (gridRef.current) {
      observer.observe(gridRef.current, {
        childList: true,
        subtree: true,
        attributes: true,
      });
    }

    return () => observer.disconnect();
  }, [calculateMasonryLayout]);

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    console.log('🚀 Drag start:', active.id);
    setActiveId(active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    console.log('🛑 Drag end:', active.id, '->', over?.id);

    if (over && active.id !== over.id) {
      const oldIndex = categories.findIndex((category) => category.id === active.id);
      const newIndex = categories.findIndex((category) => category.id === over.id);

      console.log('📝 Reordering:', oldIndex, '->', newIndex);
      
      const reorderedCategories = arrayMove(categories, oldIndex, newIndex);
      console.log('✅ New order:', reorderedCategories.map(c => c.name));
      
      onReorderCategories(reorderedCategories);
      
      // Recalculate layout after reorder
      setTimeout(calculateMasonryLayout, 50);
    }

    setActiveId(null);
  };

  const activeCategory = categories.find((category) => category.id === activeId);
  const isCategoryBeingDragged = activeId !== null;

  return (
    <CategoryDragProvider value={isCategoryBeingDragged}>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        measuring={measuring}
      >
        <SortableContext          
          items={categories.map((c) => c.id)}          
          strategy={rectSortingStrategy}        
        >
          {/* Masonry container */}
          <div 
            ref={gridRef}
            className="relative w-full"
            style={{
              height: containerHeight > 0 ? `${containerHeight}px` : 'auto',
              minHeight: '400px',
            }}
          >
            {categories.map((category, categoryIndex) => {
              const position = positions.find(p => p.id === category.id);
              const websiteCount = getWebsiteCount ? getWebsiteCount(category.id) : 0;
              
              return (
                <SortableCategory                
                  key={category.id}                
                  category={category}                
                  websiteCount={websiteCount}
                  style={{
                    position: 'absolute',
                    left: position?.x || 0,
                    top: position?.y || 0,
                    width: position?.width || 320,
                    transition: activeId === category.id ? 'none' : 'all 0.3s ease-out',
                  }}
                  onEditCategory={onEditCategory}
                  onDeleteCategory={onDeleteCategory}
                >
                  <div
                    ref={(el) => {
                      if (el) {
                        itemRefs.current.set(category.id, el);
                      }
                    }}
                  >
                    {children(category, categoryIndex)}
                  </div>
                </SortableCategory>
              );
            })}
          </div>
        </SortableContext>

        <DragOverlay
          dropAnimation={{
            sideEffects: defaultDropAnimationSideEffects({
              styles: {
                active: {
                  opacity: '0.5',
                },
              },
            }),
          }}
        >
          {activeCategory ? (
            <div className="transform rotate-3 scale-105 shadow-2xl" style={{ width: '320px' }}>
              {children(activeCategory, categories.findIndex(c => c.id === activeCategory.id))}
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>
    </CategoryDragProvider>
  );
} 