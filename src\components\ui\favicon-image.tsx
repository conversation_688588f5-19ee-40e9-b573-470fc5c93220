'use client';

import React, { useState, useCallback, memo } from 'react';
import { Globe } from 'lucide-react';
import { getFaviconFallbacks } from '@/utils';

interface FaviconImageProps {
  url: string;
  storedFaviconUrl?: string;
  alt: string;
  className?: string;
  fallback?: React.ReactNode;
}

export const FaviconImage = memo(function FaviconImage({ 
  url, 
  storedFaviconUrl,
  alt, 
  className = "w-6 h-6 rounded-sm",
  fallback 
}: FaviconImageProps) {
  const [currentFaviconIndex, setCurrentFaviconIndex] = useState(0);
  const [hasError, setHasError] = useState(false);
  
  // Create fallback URLs, using stored favicon first if available
  const fallbackUrls = React.useMemo(() => {
    const generatedFallbacks = getFaviconFallbacks(url);
    return storedFaviconUrl 
      ? [storedFaviconUrl, ...generatedFallbacks.filter(u => u !== storedFaviconUrl)]
      : generatedFallbacks;
  }, [url, storedFaviconUrl]);
  
  const currentFaviconUrl = fallbackUrls[currentFaviconIndex] || '';

  const handleError = useCallback(() => {
    if (currentFaviconIndex < fallbackUrls.length - 1) {
      setCurrentFaviconIndex(currentFaviconIndex + 1);
    } else {
      setHasError(true);
    }
  }, [currentFaviconIndex, fallbackUrls.length]);

  const handleLoad = useCallback(() => {
    setHasError(false);
  }, []);

  // Reset when URLs change
  React.useEffect(() => {
    setCurrentFaviconIndex(0);
    setHasError(false);
  }, [url, storedFaviconUrl]);

  if (hasError || !currentFaviconUrl) {
    return fallback || (
      <div className={`${className} bg-muted rounded-sm flex items-center justify-center`}>
        <Globe className="h-3 w-3 text-muted-foreground" />
      </div>
    );
  }

  return (
    /* eslint-disable-next-line @next/next/no-img-element */
    <img 
      src={currentFaviconUrl}
      alt={alt}
      className={className}
      onError={handleError}
      onLoad={handleLoad}
    />
  );
}); 