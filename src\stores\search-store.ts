import { create } from 'zustand';
import { EnhancedSearchState, SearchResults, SearchSuggestion, ExistingWebsiteSuggestion, Category, Website } from '@/types';
import { searchPopularWebsites } from '@/constants/popular-websites';

interface EnhancedSearchStore extends EnhancedSearchState {
  // Actions
  setSearchQuery: (query: string) => void;
  setSelectedCategory: (categoryId: string | null) => void;
  setSuggestions: (suggestions: SearchSuggestion[]) => void;
  setShowSuggestions: (show: boolean) => void;
  setIsSearching: (searching: boolean) => void;
  setSearchResults: (results: SearchResults) => void;
  clearSearch: () => void;
  clearFilters: () => void;
  clearAll: () => void;
  
  // Search functionality
  searchExistingWebsites: (categories: Category[], websites: Website[], query: string) => ExistingWebsiteSuggestion[];
  performSearch: (categories: Category[], websites: Website[], query: string) => SearchResults;
  updateSuggestions: (categories: Category[], websites: Website[], query: string) => void;
}

export const useSearchStore = create<EnhancedSearchStore>()((set, get) => ({
  // Initial state
  query: '',
  selectedCategory: null,
  suggestions: [],
  showSuggestions: false,
  isSearching: false,
  searchResults: {
    categories: [],
    websites: [],
    suggestions: [],
    hasResults: false
  },

  // Actions
  setSearchQuery: (query) => {
    set({ query: query.trim() });
  },

  setSelectedCategory: (categoryId) => {
    set({ selectedCategory: categoryId });
  },

  setSuggestions: (suggestions) => {
    set({ suggestions });
  },

  setShowSuggestions: (show) => {
    set({ showSuggestions: show });
  },

  setIsSearching: (searching) => {
    set({ isSearching: searching });
  },

  setSearchResults: (results) => {
    set({ searchResults: results });
  },

  clearSearch: () => {
    set({ 
      query: '',
      suggestions: [],
      showSuggestions: false,
      searchResults: {
        categories: [],
        websites: [],
        suggestions: [],
        hasResults: false
      }
    });
  },

  clearFilters: () => {
    set({ selectedCategory: null });
  },

  clearAll: () => {
    set({ 
      query: '',
      selectedCategory: null,
      suggestions: [],
      showSuggestions: false,
      searchResults: {
        categories: [],
        websites: [],
        suggestions: [],
        hasResults: false
      }
    });
  },

  // Search existing websites in user's categories
  searchExistingWebsites: (categories, websites, query) => {
    if (!query || query.trim().length < 1) return [];

    const searchTerm = query.toLowerCase().trim();
    const isSingleChar = searchTerm.length === 1;
    const existingSuggestions: ExistingWebsiteSuggestion[] = [];

    websites.forEach(website => {
      const category = categories.find(cat => cat.id === website.categoryId);
      if (!category) return;

      let score = 0;

      // Website name match (highest priority)
      const websiteName = website.name.toLowerCase();
      if (isSingleChar) {
        // For single character searches, only match websites that START with that character
        if (websiteName.startsWith(searchTerm)) {
          score += 10;
        }
      } else {
        // For multi-character searches, include partial matches
        if (websiteName.includes(searchTerm)) {
          score += websiteName.indexOf(searchTerm) === 0 ? 10 : 5;
        }
      }

      // URL match (only for multi-character searches to avoid too many results)
      if (!isSingleChar && website.url.toLowerCase().includes(searchTerm)) {
        score += 3;
      }

      // Description match (only for multi-character searches)
      if (!isSingleChar && website.description?.toLowerCase().includes(searchTerm)) {
        score += 2;
      }

      // Category name match (only for multi-character searches)
      if (!isSingleChar && category.name.toLowerCase().includes(searchTerm)) {
        score += 1;
      }

      if (score > 0) {
        existingSuggestions.push({
          type: 'existing',
          website,
          category,
          matchScore: score
        });
      }
    });

    return existingSuggestions
      .sort((a, b) => b.matchScore - a.matchScore)
      .slice(0, 5);
  },

  // Perform comprehensive search
  performSearch: (categories, websites, query) => {
    if (!query || query.trim().length < 1) {
      return {
        categories: [],
        websites: [],
        suggestions: [],
        hasResults: false
      };
    }

    const searchTerm = query.toLowerCase().trim();
    const isSingleChar = searchTerm.length === 1;
    
    // Find matching websites with priority scoring
    const websiteMatches = websites.map(website => {
      const category = categories.find(cat => cat.id === website.categoryId);
      if (!category) return null;

      let score = 0;
      const websiteName = website.name.toLowerCase();

      if (isSingleChar) {
        // For single character searches, only match websites that START with that character
        if (websiteName.startsWith(searchTerm)) {
          score += 10;
        }
      } else {
        // For multi-character searches, include partial matches with priority scoring
        if (websiteName.includes(searchTerm)) {
          score += websiteName.indexOf(searchTerm) === 0 ? 10 : 5; // Higher score for prefix matches
        }
        
        // URL match
        if (website.url.toLowerCase().includes(searchTerm)) {
          score += 3;
        }
        
        // Description match
        if (website.description?.toLowerCase().includes(searchTerm)) {
          score += 2;
        }
        
        // Category name match
        if (category.name.toLowerCase().includes(searchTerm)) {
          score += 1;
        }
      }

      return score > 0 ? { website, category, score } : null;
    }).filter(Boolean) as Array<{ website: Website; category: Category; score: number }>;

    // Sort by score (highest first) and extract websites
    const sortedMatches = websiteMatches.sort((a, b) => b.score - a.score);
    const matchingWebsites = sortedMatches.map(match => match.website);

    // Find categories that contain matching websites
    const categoryIds = new Set(matchingWebsites.map(w => w.categoryId));
    const matchingCategories = categories.filter(cat => categoryIds.has(cat.id));

    // Sort categories by the highest score of their websites
    const categoriesWithScores = matchingCategories.map(category => {
      const categoryWebsiteMatches = sortedMatches.filter(match => match.website.categoryId === category.id);
      const maxScore = Math.max(...categoryWebsiteMatches.map(match => match.score));
      return { category, maxScore };
    });

    categoriesWithScores.sort((a, b) => b.maxScore - a.maxScore);
    const sortedMatchingCategories = categoriesWithScores.map(item => item.category);

    // Also include categories whose names match the search (only for multi-character searches)
    const categoriesWithNameMatch = !isSingleChar 
      ? categories.filter(cat => 
          cat.name.toLowerCase().includes(searchTerm) && !categoryIds.has(cat.id)
        )
      : [];

    const allMatchingCategories = [...sortedMatchingCategories, ...categoriesWithNameMatch];

    // Get existing website suggestions (allow single character)
    const existingSuggestions = get().searchExistingWebsites(categories, websites, query);

    // Get popular website suggestions (require 2+ characters to avoid overwhelming results)
    const existingUrls = new Set(websites.map(w => new URL(w.url).hostname));
    const popularSuggestions = query.trim().length >= 2 
      ? searchPopularWebsites(query, 5).filter(suggestion => !existingUrls.has(new URL(suggestion.url).hostname))
      : [];

    const allSuggestions: SearchSuggestion[] = [
      ...existingSuggestions,
      ...popularSuggestions
    ];

    return {
      categories: allMatchingCategories,
      websites: matchingWebsites,
      suggestions: allSuggestions,
      hasResults: allMatchingCategories.length > 0 || matchingWebsites.length > 0 || allSuggestions.length > 0
    };
  },

  // Update suggestions based on current query
  updateSuggestions: (categories, websites, query) => {
    if (!query || query.trim().length < 1) {
      set({ suggestions: [], showSuggestions: false });
      return;
    }

    // Get existing website suggestions (allow single character) - prioritize these
    const existingSuggestions = get().searchExistingWebsites(categories, websites, query);
    
    // Get popular website suggestions (require 2+ characters to avoid overwhelming results)
    const existingUrls = new Set(websites.map(w => new URL(w.url).hostname));
    const popularSuggestions = query.trim().length >= 2 
      ? searchPopularWebsites(query, 4).filter(suggestion => !existingUrls.has(new URL(suggestion.url).hostname))
      : [];

    // Prioritize existing websites - always show them first
    const prioritizedSuggestions: SearchSuggestion[] = [
      ...existingSuggestions.slice(0, 4), // Limit existing to 4 to save space
      ...popularSuggestions.slice(0, 3)   // Limit popular to 3
    ];

    set({ 
      suggestions: prioritizedSuggestions.slice(0, 6), // Total limit of 6 suggestions
      showSuggestions: prioritizedSuggestions.length > 0 
    });
  }
}));

// Enhanced hook for easy access to search functionality
export function useSearch() {
  const query = useSearchStore((state) => state.query);
  const selectedCategory = useSearchStore((state) => state.selectedCategory);
  const suggestions = useSearchStore((state) => state.suggestions);
  const showSuggestions = useSearchStore((state) => state.showSuggestions);
  const isSearching = useSearchStore((state) => state.isSearching);
  const searchResults = useSearchStore((state) => state.searchResults);
  
  const setSearchQuery = useSearchStore((state) => state.setSearchQuery);
  const setSelectedCategory = useSearchStore((state) => state.setSelectedCategory);
  const setSuggestions = useSearchStore((state) => state.setSuggestions);
  const setShowSuggestions = useSearchStore((state) => state.setShowSuggestions);
  const setIsSearching = useSearchStore((state) => state.setIsSearching);
  const setSearchResults = useSearchStore((state) => state.setSearchResults);
  const clearSearch = useSearchStore((state) => state.clearSearch);
  const clearFilters = useSearchStore((state) => state.clearFilters);
  const clearAll = useSearchStore((state) => state.clearAll);
  
  const searchExistingWebsites = useSearchStore((state) => state.searchExistingWebsites);
  const performSearch = useSearchStore((state) => state.performSearch);
  const updateSuggestions = useSearchStore((state) => state.updateSuggestions);

  return {
    // State
    query,
    selectedCategory,
    suggestions,
    showSuggestions,
    isSearching,
    searchResults,
    
    // Actions
    setSearchQuery,
    setSelectedCategory,
    setSuggestions,
    setShowSuggestions,
    setIsSearching,
    setSearchResults,
    clearSearch,
    clearFilters,
    clearAll,
    
    // Search functions
    searchExistingWebsites,
    performSearch,
    updateSuggestions
  };
} 