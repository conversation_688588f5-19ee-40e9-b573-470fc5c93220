import { NewWebsiteSuggestion } from '@/types';

export const POPULAR_WEBSITES: NewWebsiteSuggestion[] = [
  // Development & Coding
  {
    type: 'new',
    name: 'GitHub',
    url: 'https://github.com',
    description: 'Code hosting platform for version control and collaboration',
    category: 'Development',
    tags: ['git', 'code', 'repository', 'version control']
  },
  {
    type: 'new',
    name: 'Stack Overflow',
    url: 'https://stackoverflow.com',
    description: 'Q&A platform for developers',
    category: 'Development',
    tags: ['questions', 'answers', 'programming', 'help']
  },
  {
    type: 'new',
    name: 'CodePen',
    url: 'https://codepen.io',
    description: 'Online code editor and sharing platform',
    category: 'Development',
    tags: ['code', 'editor', 'frontend', 'demo']
  },
  {
    type: 'new',
    name: 'VS Code',
    url: 'https://code.visualstudio.com',
    description: 'Free code editor with powerful features',
    category: 'Development',
    tags: ['editor', 'ide', 'microsoft', 'coding']
  },
  {
    type: 'new',
    name: 'Vercel',
    url: 'https://vercel.com',
    description: 'Deploy and host web applications',
    category: 'Development',
    tags: ['deployment', 'hosting', 'frontend', 'nextjs']
  },
  {
    type: 'new',
    name: 'Netlify',
    url: 'https://netlify.com',
    description: 'Build, deploy, and manage modern web projects',
    category: 'Development',
    tags: ['deployment', 'hosting', 'jamstack', 'static']
  },

  // Design Tools
  {
    type: 'new',
    name: 'Figma',
    url: 'https://figma.com',
    description: 'Collaborative design tool',
    category: 'Design',
    tags: ['design', 'ui', 'ux', 'prototyping', 'collaboration']
  },
  {
    type: 'new',
    name: 'Adobe Creative Cloud',
    url: 'https://adobe.com',
    description: 'Creative software suite',
    category: 'Design',
    tags: ['photoshop', 'illustrator', 'creative', 'adobe']
  },
  {
    type: 'new',
    name: 'Canva',
    url: 'https://canva.com',
    description: 'Easy-to-use design platform',
    category: 'Design',
    tags: ['design', 'templates', 'graphics', 'easy']
  },
  {
    type: 'new',
    name: 'Dribbble',
    url: 'https://dribbble.com',
    description: 'Design inspiration and portfolio platform',
    category: 'Design',
    tags: ['inspiration', 'portfolio', 'designers', 'showcase']
  },

  // Productivity
  {
    type: 'new',
    name: 'Notion',
    url: 'https://notion.so',
    description: 'All-in-one workspace for notes, docs, and projects',
    category: 'Productivity',
    tags: ['notes', 'docs', 'workspace', 'organization']
  },
  {
    type: 'new',
    name: 'Slack',
    url: 'https://slack.com',
    description: 'Team communication and collaboration',
    category: 'Productivity',
    tags: ['chat', 'team', 'communication', 'collaboration']
  },
  {
    type: 'new',
    name: 'Trello',
    url: 'https://trello.com',
    description: 'Visual project management with boards and cards',
    category: 'Productivity',
    tags: ['project management', 'kanban', 'organization', 'tasks']
  },
  {
    type: 'new',
    name: 'Asana',
    url: 'https://asana.com',
    description: 'Team project and task management',
    category: 'Productivity',
    tags: ['project management', 'tasks', 'team', 'workflow']
  },
  {
    type: 'new',
    name: 'Discord',
    url: 'https://discord.com',
    description: 'Voice, video, and text communication',
    category: 'Productivity',
    tags: ['chat', 'voice', 'gaming', 'community']
  },

  // Security & Privacy
  {
    type: 'new',
    name: 'Bitwarden',
    url: 'https://bitwarden.com',
    description: 'Open source password manager',
    category: 'Security',
    tags: ['password', 'security', 'vault', 'privacy']
  },
  {
    type: 'new',
    name: '1Password',
    url: 'https://1password.com',
    description: 'Password manager and digital vault',
    category: 'Security',
    tags: ['password', 'security', 'vault', 'privacy']
  },
  {
    type: 'new',
    name: 'ProtonMail',
    url: 'https://protonmail.com',
    description: 'Secure and private email service',
    category: 'Security',
    tags: ['email', 'privacy', 'encryption', 'secure']
  },
  {
    type: 'new',
    name: 'NordVPN',
    url: 'https://nordvpn.com',
    description: 'Virtual private network service',
    category: 'Security',
    tags: ['vpn', 'privacy', 'security', 'network']
  },

  // Learning & Documentation
  {
    type: 'new',
    name: 'MDN Web Docs',
    url: 'https://developer.mozilla.org',
    description: 'Web development documentation',
    category: 'Learning',
    tags: ['documentation', 'web', 'javascript', 'css', 'html']
  },
  {
    type: 'new',
    name: 'React Documentation',
    url: 'https://react.dev',
    description: 'Official React documentation',
    category: 'Learning',
    tags: ['react', 'documentation', 'javascript', 'frontend']
  },
  {
    type: 'new',
    name: 'YouTube',
    url: 'https://youtube.com',
    description: 'Video sharing and learning platform',
    category: 'Learning',
    tags: ['video', 'tutorials', 'education', 'entertainment']
  },
  {
    type: 'new',
    name: 'Coursera',
    url: 'https://coursera.org',
    description: 'Online courses and certifications',
    category: 'Learning',
    tags: ['courses', 'education', 'certification', 'learning']
  },

  // Cloud & Hosting
  {
    type: 'new',
    name: 'AWS',
    url: 'https://aws.amazon.com',
    description: 'Amazon Web Services cloud platform',
    category: 'Cloud',
    tags: ['cloud', 'hosting', 'amazon', 'infrastructure']
  },
  {
    type: 'new',
    name: 'Google Cloud',
    url: 'https://cloud.google.com',
    description: 'Google cloud computing platform',
    category: 'Cloud',
    tags: ['cloud', 'hosting', 'google', 'infrastructure']
  },
  {
    type: 'new',
    name: 'DigitalOcean',
    url: 'https://digitalocean.com',
    description: 'Cloud infrastructure for developers',
    category: 'Cloud',
    tags: ['cloud', 'hosting', 'droplets', 'infrastructure']
  },

  // Social & Communication
  {
    type: 'new',
    name: 'Twitter',
    url: 'https://twitter.com',
    description: 'Social media and microblogging platform',
    category: 'Social',
    tags: ['social media', 'twitter', 'news', 'networking']
  },
  {
    type: 'new',
    name: 'LinkedIn',
    url: 'https://linkedin.com',
    description: 'Professional networking platform',
    category: 'Social',
    tags: ['professional', 'networking', 'jobs', 'career']
  },
  {
    type: 'new',
    name: 'Reddit',
    url: 'https://reddit.com',
    description: 'Social news aggregation and discussion',
    category: 'Social',
    tags: ['social media', 'news', 'discussion', 'community']
  },

  // AI & ML
  {
    type: 'new',
    name: 'ChatGPT',
    url: 'https://chat.openai.com',
    description: 'AI chatbot for conversations and assistance',
    category: 'AI',
    tags: ['ai', 'chatbot', 'openai', 'assistant']
  },
  {
    type: 'new',
    name: 'Claude',
    url: 'https://claude.ai',
    description: 'AI assistant by Anthropic',
    category: 'AI',
    tags: ['ai', 'assistant', 'anthropic', 'chatbot']
  },
  {
    type: 'new',
    name: 'Midjourney',
    url: 'https://midjourney.com',
    description: 'AI image generation tool',
    category: 'AI',
    tags: ['ai', 'image generation', 'art', 'creative']
  },

  // Browsers
  {
    type: 'new',
    name: 'Brave',
    url: 'https://brave.com',
    description: 'Privacy-focused web browser with ad blocking',
    category: 'Browser',
    tags: ['browser', 'privacy', 'ad blocking', 'crypto']
  },
  {
    type: 'new',
    name: 'Firefox',
    url: 'https://firefox.com',
    description: 'Open-source web browser by Mozilla',
    category: 'Browser',
    tags: ['browser', 'open source', 'mozilla', 'privacy']
  },
  {
    type: 'new',
    name: 'Opera',
    url: 'https://opera.com',
    description: 'Fast web browser with built-in VPN',
    category: 'Browser',
    tags: ['browser', 'vpn', 'fast', 'workspaces']
  },
  {
    type: 'new',
    name: 'Microsoft Edge',
    url: 'https://microsoft.com/edge',
    description: 'Modern web browser by Microsoft',
    category: 'Browser',
    tags: ['browser', 'microsoft', 'chromium', 'modern']
  },

  // Entertainment & Media
  {
    type: 'new',
    name: 'Netflix',
    url: 'https://netflix.com',
    description: 'Streaming service for movies and TV shows',
    category: 'Entertainment',
    tags: ['streaming', 'movies', 'tv shows', 'entertainment']
  },
  {
    type: 'new',
    name: 'Spotify',
    url: 'https://spotify.com',
    description: 'Music streaming platform',
    category: 'Entertainment',
    tags: ['music', 'streaming', 'playlists', 'podcasts']
  },
  {
    type: 'new',
    name: 'Twitch',
    url: 'https://twitch.tv',
    description: 'Live streaming platform for gaming and entertainment',
    category: 'Entertainment',
    tags: ['streaming', 'gaming', 'live', 'content']
  },
  {
    type: 'new',
    name: 'Instagram',
    url: 'https://instagram.com',
    description: 'Photo and video sharing social network',
    category: 'Entertainment',
    tags: ['social media', 'photos', 'videos', 'stories']
  },
  {
    type: 'new',
    name: 'TikTok',
    url: 'https://tiktok.com',
    description: 'Short-form video sharing platform',
    category: 'Entertainment',
    tags: ['videos', 'short form', 'social media', 'trends']
  },
  {
    type: 'new',
    name: 'Pinterest',
    url: 'https://pinterest.com',
    description: 'Visual discovery and idea platform',
    category: 'Entertainment',
    tags: ['visual', 'ideas', 'inspiration', 'boards']
  },
  {
    type: 'new',
    name: 'Disney+',
    url: 'https://disneyplus.com',
    description: 'Disney streaming service',
    category: 'Entertainment',
    tags: ['streaming', 'disney', 'movies', 'family']
  },

  // E-commerce
  {
    type: 'new',
    name: 'Amazon',
    url: 'https://amazon.com',
    description: 'Online marketplace and e-commerce platform',
    category: 'E-commerce',
    tags: ['shopping', 'marketplace', 'delivery', 'prime']
  },
  {
    type: 'new',
    name: 'eBay',
    url: 'https://ebay.com',
    description: 'Online auction and marketplace',
    category: 'E-commerce',
    tags: ['auction', 'marketplace', 'buying', 'selling']
  },
  {
    type: 'new',
    name: 'Shopify',
    url: 'https://shopify.com',
    description: 'E-commerce platform for online stores',
    category: 'E-commerce',
    tags: ['e-commerce', 'online store', 'platform', 'business']
  },
  {
    type: 'new',
    name: 'Etsy',
    url: 'https://etsy.com',
    description: 'Marketplace for handmade and vintage items',
    category: 'E-commerce',
    tags: ['handmade', 'vintage', 'crafts', 'unique']
  },
  {
    type: 'new',
    name: 'AliExpress',
    url: 'https://aliexpress.com',
    description: 'Global online retail marketplace',
    category: 'E-commerce',
    tags: ['marketplace', 'international', 'affordable', 'wholesale']
  },

  // Finance & Crypto
  {
    type: 'new',
    name: 'PayPal',
    url: 'https://paypal.com',
    description: 'Digital payment platform',
    category: 'Finance',
    tags: ['payments', 'digital wallet', 'money transfer', 'secure']
  },
  {
    type: 'new',
    name: 'Stripe',
    url: 'https://stripe.com',
    description: 'Online payment processing for businesses',
    category: 'Finance',
    tags: ['payments', 'api', 'business', 'developers']
  },
  {
    type: 'new',
    name: 'Coinbase',
    url: 'https://coinbase.com',
    description: 'Cryptocurrency exchange and wallet',
    category: 'Finance',
    tags: ['cryptocurrency', 'bitcoin', 'exchange', 'wallet']
  },
  {
    type: 'new',
    name: 'Binance',
    url: 'https://binance.com',
    description: 'Cryptocurrency trading platform',
    category: 'Finance',
    tags: ['cryptocurrency', 'trading', 'exchange', 'blockchain']
  },

  // News & Media
  {
    type: 'new',
    name: 'BBC',
    url: 'https://bbc.com',
    description: 'British public service broadcaster',
    category: 'News',
    tags: ['news', 'media', 'international', 'reliable']
  },
  {
    type: 'new',
    name: 'CNN',
    url: 'https://cnn.com',
    description: 'Cable news network and website',
    category: 'News',
    tags: ['news', 'breaking news', 'politics', 'world']
  },
  {
    type: 'new',
    name: 'The New York Times',
    url: 'https://nytimes.com',
    description: 'American newspaper and digital media',
    category: 'News',
    tags: ['news', 'journalism', 'politics', 'culture']
  },
  {
    type: 'new',
    name: 'Medium',
    url: 'https://medium.com',
    description: 'Online publishing platform for writers',
    category: 'News',
    tags: ['writing', 'articles', 'blogging', 'publishing']
  },
  {
    type: 'new',
    name: 'Reuters',
    url: 'https://reuters.com',
    description: 'International news organization',
    category: 'News',
    tags: ['news', 'international', 'business', 'finance']
  },

  // Email & Communication
  {
    type: 'new',
    name: 'Gmail',
    url: 'https://gmail.com',
    description: 'Google email service',
    category: 'Email',
    tags: ['email', 'google', 'communication', 'free']
  },
  {
    type: 'new',
    name: 'Outlook',
    url: 'https://outlook.com',
    description: 'Microsoft email and calendar service',
    category: 'Email',
    tags: ['email', 'microsoft', 'calendar', 'office']
  },
  {
    type: 'new',
    name: 'Yahoo Mail',
    url: 'https://mail.yahoo.com',
    description: 'Yahoo email service',
    category: 'Email',
    tags: ['email', 'yahoo', 'communication', 'classic']
  },
  {
    type: 'new',
    name: 'Zoom',
    url: 'https://zoom.us',
    description: 'Video conferencing and communication',
    category: 'Communication',
    tags: ['video calls', 'meetings', 'webinars', 'remote work']
  },
  {
    type: 'new',
    name: 'Microsoft Teams',
    url: 'https://teams.microsoft.com',
    description: 'Collaboration platform with chat, video, and files',
    category: 'Communication',
    tags: ['collaboration', 'chat', 'video calls', 'microsoft']
  },

  // Search Engines
  {
    type: 'new',
    name: 'Google',
    url: 'https://google.com',
    description: 'World\'s most popular search engine',
    category: 'Search',
    tags: ['search', 'web search', 'information', 'google']
  },
  {
    type: 'new',
    name: 'Bing',
    url: 'https://bing.com',
    description: 'Microsoft web search engine',
    category: 'Search',
    tags: ['search', 'microsoft', 'web search', 'alternative']
  },
  {
    type: 'new',
    name: 'Perplexity',
    url: 'https://perplexity.ai',
    description: 'AI-powered search and answer engine',
    category: 'Search',
    tags: ['ai search', 'answers', 'research', 'intelligent']
  },

  // More Development Tools
  {
    type: 'new',
    name: 'Docker',
    url: 'https://docker.com',
    description: 'Containerization platform for applications',
    category: 'Development',
    tags: ['containers', 'deployment', 'devops', 'virtualization']
  },
  {
    type: 'new',
    name: 'GitLab',
    url: 'https://gitlab.com',
    description: 'DevOps platform with Git repository management',
    category: 'Development',
    tags: ['git', 'devops', 'ci/cd', 'repository']
  },
  {
    type: 'new',
    name: 'Bitbucket',
    url: 'https://bitbucket.org',
    description: 'Git repository management by Atlassian',
    category: 'Development',
    tags: ['git', 'repository', 'atlassian', 'code hosting']
  },
  {
    type: 'new',
    name: 'Heroku',
    url: 'https://heroku.com',
    description: 'Cloud platform for deploying applications',
    category: 'Development',
    tags: ['deployment', 'cloud', 'platform', 'hosting']
  },
  {
    type: 'new',
    name: 'Firebase',
    url: 'https://firebase.google.com',
    description: 'Google\'s app development platform',
    category: 'Development',
    tags: ['backend', 'database', 'authentication', 'google']
  },
  {
    type: 'new',
    name: 'Supabase',
    url: 'https://supabase.com',
    description: 'Open source Firebase alternative',
    category: 'Development',
    tags: ['backend', 'database', 'open source', 'postgresql']
  },
  {
    type: 'new',
    name: 'MongoDB',
    url: 'https://mongodb.com',
    description: 'NoSQL document database',
    category: 'Development',
    tags: ['database', 'nosql', 'document', 'mongodb']
  },
  {
    type: 'new',
    name: 'PostgreSQL',
    url: 'https://postgresql.org',
    description: 'Advanced open source relational database',
    category: 'Development',
    tags: ['database', 'sql', 'relational', 'open source']
  },
  {
    type: 'new',
    name: 'Jenkins',
    url: 'https://jenkins.io',
    description: 'Open source automation server for CI/CD',
    category: 'Development',
    tags: ['ci/cd', 'automation', 'deployment', 'open source']
  },
  {
    type: 'new',
    name: 'Kubernetes',
    url: 'https://kubernetes.io',
    description: 'Container orchestration platform',
    category: 'Development',
    tags: ['containers', 'orchestration', 'kubernetes', 'devops']
  },
  {
    type: 'new',
    name: 'Postman',
    url: 'https://postman.com',
    description: 'API development and testing platform',
    category: 'Development',
    tags: ['api', 'testing', 'development', 'rest']
  },

  // More Design Tools
  {
    type: 'new',
    name: 'Sketch',
    url: 'https://sketch.com',
    description: 'Digital design toolkit for Mac',
    category: 'Design',
    tags: ['design', 'ui', 'mac', 'vector']
  },
  {
    type: 'new',
    name: 'InVision',
    url: 'https://invisionapp.com',
    description: 'Digital product design platform',
    category: 'Design',
    tags: ['prototyping', 'design', 'collaboration', 'ui']
  },
  {
    type: 'new',
    name: 'Adobe XD',
    url: 'https://adobe.com/products/xd',
    description: 'UI/UX design and prototyping tool',
    category: 'Design',
    tags: ['ui', 'ux', 'prototyping', 'adobe']
  },
  {
    type: 'new',
    name: 'Framer',
    url: 'https://framer.com',
    description: 'Interactive design and prototyping',
    category: 'Design',
    tags: ['prototyping', 'interactive', 'design', 'animation']
  },
  {
    type: 'new',
    name: 'Webflow',
    url: 'https://webflow.com',
    description: 'Visual web design platform',
    category: 'Design',
    tags: ['web design', 'visual', 'no code', 'cms']
  },

  // More Productivity Tools
  {
    type: 'new',
    name: 'Microsoft Office',
    url: 'https://office.com',
    description: 'Office productivity suite',
    category: 'Productivity',
    tags: ['office', 'word', 'excel', 'powerpoint']
  },
  {
    type: 'new',
    name: 'Google Workspace',
    url: 'https://workspace.google.com',
    description: 'Google\'s productivity and collaboration tools',
    category: 'Productivity',
    tags: ['docs', 'sheets', 'slides', 'collaboration']
  },
  {
    type: 'new',
    name: 'Calendly',
    url: 'https://calendly.com',
    description: 'Scheduling and calendar management',
    category: 'Productivity',
    tags: ['scheduling', 'calendar', 'meetings', 'appointments']
  },
  {
    type: 'new',
    name: 'Todoist',
    url: 'https://todoist.com',
    description: 'Task management and to-do lists',
    category: 'Productivity',
    tags: ['tasks', 'todo', 'productivity', 'organization']
  },
  {
    type: 'new',
    name: 'Evernote',
    url: 'https://evernote.com',
    description: 'Note-taking and organization app',
    category: 'Productivity',
    tags: ['notes', 'organization', 'productivity', 'sync']
  },

  // More Security Tools
  {
    type: 'new',
    name: 'LastPass',
    url: 'https://lastpass.com',
    description: 'Password manager and digital vault',
    category: 'Security',
    tags: ['password', 'security', 'vault', 'manager']
  },
  {
    type: 'new',
    name: 'Dashlane',
    url: 'https://dashlane.com',
    description: 'Password manager with security features',
    category: 'Security',
    tags: ['password', 'security', 'identity', 'protection']
  },
  {
    type: 'new',
    name: 'Tor Browser',
    url: 'https://torproject.org',
    description: 'Anonymous web browser for privacy',
    category: 'Security',
    tags: ['privacy', 'anonymous', 'browser', 'tor']
  },
  {
    type: 'new',
    name: 'ExpressVPN',
    url: 'https://expressvpn.com',
    description: 'Premium VPN service for privacy',
    category: 'Security',
    tags: ['vpn', 'privacy', 'security', 'anonymous']
  },

  // More Learning Platforms
  {
    type: 'new',
    name: 'Khan Academy',
    url: 'https://khanacademy.org',
    description: 'Free online courses and lessons',
    category: 'Learning',
    tags: ['education', 'free', 'courses', 'math']
  },
  {
    type: 'new',
    name: 'Udemy',
    url: 'https://udemy.com',
    description: 'Online learning marketplace',
    category: 'Learning',
    tags: ['courses', 'online learning', 'skills', 'education']
  },
  {
    type: 'new',
    name: 'Pluralsight',
    url: 'https://pluralsight.com',
    description: 'Technology and creative skills platform',
    category: 'Learning',
    tags: ['technology', 'programming', 'skills', 'professional']
  },
  {
    type: 'new',
    name: 'FreeCodeCamp',
    url: 'https://freecodecamp.org',
    description: 'Free coding education platform',
    category: 'Learning',
    tags: ['coding', 'programming', 'free', 'certificates']
  },
  {
    type: 'new',
    name: 'W3Schools',
    url: 'https://w3schools.com',
    description: 'Web development tutorials and references',
    category: 'Learning',
    tags: ['web development', 'html', 'css', 'javascript']
  },

  // More Cloud Services
  {
    type: 'new',
    name: 'Microsoft Azure',
    url: 'https://azure.microsoft.com',
    description: 'Microsoft cloud computing platform',
    category: 'Cloud',
    tags: ['cloud', 'microsoft', 'azure', 'enterprise']
  },
  {
    type: 'new',
    name: 'IBM Cloud',
    url: 'https://cloud.ibm.com',
    description: 'IBM\'s cloud computing platform',
    category: 'Cloud',
    tags: ['cloud', 'ibm', 'enterprise', 'ai']
  },
  {
    type: 'new',
    name: 'Oracle Cloud',
    url: 'https://oracle.com/cloud',
    description: 'Oracle\'s cloud infrastructure platform',
    category: 'Cloud',
    tags: ['cloud', 'oracle', 'database', 'enterprise']
  },
  {
    type: 'new',
    name: 'Cloudflare',
    url: 'https://cloudflare.com',
    description: 'Web performance and security platform',
    category: 'Cloud',
    tags: ['cdn', 'security', 'performance', 'dns']
  },

  // More AI Tools
  {
    type: 'new',
    name: 'Gemini',
    url: 'https://gemini.google.com',
    description: 'Google\'s AI assistant and chatbot',
    category: 'AI',
    tags: ['ai', 'google', 'assistant', 'chatbot']
  },
  {
    type: 'new',
    name: 'GitHub Copilot',
    url: 'https://github.com/features/copilot',
    description: 'AI pair programmer for coding',
    category: 'AI',
    tags: ['ai', 'coding', 'programming', 'github']
  },
  {
    type: 'new',
    name: 'Hugging Face',
    url: 'https://huggingface.co',
    description: 'AI and machine learning community platform',
    category: 'AI',
    tags: ['ai', 'machine learning', 'models', 'community']
  },
  {
    type: 'new',
    name: 'Stable Diffusion',
    url: 'https://stability.ai',
    description: 'AI image generation platform',
    category: 'AI',
    tags: ['ai', 'image generation', 'stable diffusion', 'art']
  },

  // Gaming Platforms
  {
    type: 'new',
    name: 'Steam',
    url: 'https://store.steampowered.com',
    description: 'Digital game distribution platform',
    category: 'Gaming',
    tags: ['games', 'pc gaming', 'digital store', 'steam']
  },
  {
    type: 'new',
    name: 'Epic Games',
    url: 'https://epicgames.com',
    description: 'Game developer and digital storefront',
    category: 'Gaming',
    tags: ['games', 'epic store', 'fortnite', 'unreal']
  },
  {
    type: 'new',
    name: 'PlayStation',
    url: 'https://playstation.com',
    description: 'Sony gaming console and services',
    category: 'Gaming',
    tags: ['gaming', 'console', 'playstation', 'sony']
  },
  {
    type: 'new',
    name: 'Xbox',
    url: 'https://xbox.com',
    description: 'Microsoft gaming platform and services',
    category: 'Gaming',
    tags: ['gaming', 'console', 'xbox', 'microsoft']
  },
  {
    type: 'new',
    name: 'Nintendo',
    url: 'https://nintendo.com',
    description: 'Nintendo gaming platform and games',
    category: 'Gaming',
    tags: ['gaming', 'nintendo', 'switch', 'mario']
  }
];

// Function to search popular websites
export function searchPopularWebsites(query: string, limit: number = 5): NewWebsiteSuggestion[] {
  if (!query || query.trim().length < 2) return [];

  const searchTerm = query.toLowerCase().trim();
  
  const scored = POPULAR_WEBSITES.map(website => {
    let score = 0;
    
    // Name match (highest priority)
    if (website.name.toLowerCase().includes(searchTerm)) {
      score += website.name.toLowerCase().indexOf(searchTerm) === 0 ? 10 : 5;
    }
    
    // Description match
    if (website.description?.toLowerCase().includes(searchTerm)) {
      score += 3;
    }
    
    // Tags match
    const tagMatch = website.tags?.some(tag => tag.toLowerCase().includes(searchTerm));
    if (tagMatch) {
      score += 2;
    }
    
    // URL match (domain)
    if (website.url.toLowerCase().includes(searchTerm)) {
      score += 1;
    }
    
    return { ...website, score };
  });
  
  return scored
    .filter(website => website.score > 0)
    .sort((a, b) => b.score - a.score)
    .slice(0, limit);
} 