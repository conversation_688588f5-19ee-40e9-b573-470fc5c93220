'use client';

import * as React from 'react';
import { Search, X, Plus, ExternalLink, Globe } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/utils';
import { Input } from './input';
import { Button } from './button';
import { Card } from './card';
import { SearchSuggestion, ExistingWebsiteSuggestion, NewWebsiteSuggestion } from '@/types';
import { FaviconImage } from '@/components/ui/favicon-image';

export interface SearchInputWithSuggestionsProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  value: string;
  onChange: (value: string) => void;
  onClear?: () => void;
  placeholder?: string;
  className?: string;
  suggestions: SearchSuggestion[];
  showSuggestions: boolean;
  onSuggestionClick: (suggestion: SearchSuggestion) => void;
  onAddWebsite?: (suggestion: NewWebsiteSuggestion) => void;
  onAddExistingWebsite?: (suggestion: ExistingWebsiteSuggestion) => void;
  loading?: boolean;
}

const SearchInputWithSuggestions = React.forwardRef<HTMLInputElement, SearchInputWithSuggestionsProps>(
  ({ 
    className, 
    value, 
    onChange, 
    onClear, 
    placeholder = 'Search websites...', 
    suggestions,
    showSuggestions,
    onSuggestionClick,
    onAddWebsite,
    onAddExistingWebsite,
    loading = false,
    ...props 
  }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false);
    const [selectedIndex, setSelectedIndex] = React.useState(-1);
    const containerRef = React.useRef<HTMLDivElement>(null);
    const inputRef = React.useRef<HTMLInputElement>(null);

    // Combine refs
    React.useImperativeHandle(ref, () => inputRef.current as HTMLInputElement);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      onChange(e.target.value);
      setSelectedIndex(-1);
    };

    const handleClear = () => {
      onChange('');
      onClear?.();
      setSelectedIndex(-1);
      inputRef.current?.focus();
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (!showSuggestions || suggestions.length === 0) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < suggestions.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : suggestions.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
            onSuggestionClick(suggestions[selectedIndex]);
          }
          break;
        case 'Escape':
          setSelectedIndex(-1);
          inputRef.current?.blur();
          break;
      }
    };

    const handleSuggestionClick = (suggestion: SearchSuggestion, index: number) => {
      setSelectedIndex(index);
      onSuggestionClick(suggestion);
    };

    const handleAddClick = (e: React.MouseEvent, suggestion: NewWebsiteSuggestion) => {
      e.stopPropagation();
      e.preventDefault();
      
      console.log('🔧 Add button clicked for:', suggestion.name);
      
      // Force hide suggestions immediately
      setIsFocused(false);
      setSelectedIndex(-1);
      
      // Call the add website handler
      onAddWebsite?.(suggestion);
    };

    const handleAddExistingClick = (e: React.MouseEvent, suggestion: ExistingWebsiteSuggestion) => {
      e.stopPropagation();
      e.preventDefault();
      
      console.log('🔧 Add existing website button clicked for:', suggestion.website.name);
      
      // Force hide suggestions immediately
      setIsFocused(false);
      setSelectedIndex(-1);
      
      // Call the add existing website handler
      onAddExistingWebsite?.(suggestion);
    };

    // Close suggestions when clicking outside
    React.useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
          setIsFocused(false);
          setSelectedIndex(-1);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const shouldShowSuggestions = isFocused && showSuggestions && suggestions.length > 0;

    return (
      <div ref={containerRef} className={cn('relative', className)}>
        <div className="relative flex items-center">
          <Search className="absolute left-3 h-4 w-4 text-muted-foreground" />
          <Input
            ref={inputRef}
            type="text"
            placeholder={placeholder}
            value={value}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            onFocus={() => setIsFocused(true)}
            className="pl-10 pr-10"
            {...props}
          />
          {value && (
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="absolute right-1 h-8 w-8 text-muted-foreground hover:text-foreground"
              onClick={handleClear}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Clear search</span>
            </Button>
          )}
          {loading && (
            <div className="absolute right-3 h-4 w-4">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            </div>
          )}
        </div>

        <AnimatePresence>
          {shouldShowSuggestions && (
            <motion.div
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
              transition={{ duration: 0.15 }}
              className="absolute top-full left-0 right-0 z-50 mt-1"
            >
              <Card className="max-h-80 overflow-y-auto shadow-lg">
                <div className="p-2 space-y-1">
                  {suggestions.map((suggestion, index) => (
                    <motion.div
                      key={`${suggestion.type}-${suggestion.type === 'existing' ? suggestion.website.id : suggestion.name}`}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className={cn(
                        'group flex items-center justify-between p-2 rounded-md transition-colors',
                        selectedIndex === index
                          ? 'bg-primary/10 text-primary'
                          : 'hover:bg-muted/50'
                      )}
                    >
                      {suggestion.type === 'existing' ? (
                        <ExistingSuggestionItem 
                          suggestion={suggestion} 
                          onAddClick={(e) => handleAddExistingClick(e, suggestion)}
                          onNameClick={() => handleSuggestionClick(suggestion, index)}
                        />
                      ) : (
                        <NewSuggestionItem 
                          suggestion={suggestion} 
                          onAddClick={(e) => handleAddClick(e, suggestion)}
                          onNameClick={() => handleSuggestionClick(suggestion, index)}
                        />
                      )}
                    </motion.div>
                  ))}
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }
);

SearchInputWithSuggestions.displayName = 'SearchInputWithSuggestions';

// Component for existing website suggestions
function ExistingSuggestionItem({ 
  suggestion, 
  onAddClick,
  onNameClick
}: { 
  suggestion: ExistingWebsiteSuggestion;
  onAddClick: (e: React.MouseEvent) => void;
  onNameClick: () => void;
}) {
  return (
    <>
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        <FaviconImage
          url={suggestion.website.url}
          storedFaviconUrl={suggestion.website.faviconUrl}
          alt={suggestion.website.name}
          className="w-4 h-4 rounded flex-shrink-0"
          fallback={<Globe className="w-4 h-4 text-muted-foreground" />}
        />
        <div className="flex-1 min-w-0">
          <div className="font-medium text-sm truncate">
            <span 
              className="cursor-pointer hover:text-primary transition-colors"
              onClick={onNameClick}
            >
              {suggestion.website.name}
            </span>
          </div>
          <div className="text-xs text-muted-foreground truncate">
            in {suggestion.category.name}
          </div>
        </div>
      </div>
      <div className="flex items-center space-x-1 flex-shrink-0">
        <Button
          variant="ghost"
          size="sm"
          className="h-8 px-4 text-sm"
          onClick={onAddClick}
        >
          <Plus className="w-4 h-4 mr-1" />
          Add
        </Button>
        <ExternalLink className="w-3 h-3 text-muted-foreground group-hover:text-foreground" />
      </div>
    </>
  );
}

// Component for new website suggestions
function NewSuggestionItem({ 
  suggestion, 
  onAddClick,
  onNameClick
}: { 
  suggestion: NewWebsiteSuggestion;
  onAddClick: (e: React.MouseEvent) => void;
  onNameClick: () => void;
}) {
  return (
    <>
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        <FaviconImage
          url={suggestion.url}
          alt={suggestion.name}
          className="w-4 h-4 rounded flex-shrink-0"
          fallback={<Globe className="w-4 h-4 text-muted-foreground" />}
        />
        <div className="flex-1 min-w-0">
          <div className="font-medium text-sm truncate">
            <span 
              className="cursor-pointer hover:text-primary transition-colors"
              onClick={onNameClick}
            >
              {suggestion.name}
            </span>
          </div>
          <div className="text-xs text-muted-foreground truncate">
            {suggestion.description}
          </div>
        </div>
      </div>
      <div className="flex items-center space-x-1 flex-shrink-0">
        <Button
          variant="ghost"
          size="sm"
          className="h-8 px-4 text-sm"
          onClick={(e) => onAddClick(e)}
        >
          <Plus className="w-4 h-4 mr-1" />
          Add
        </Button>
        <ExternalLink className="w-3 h-3 text-muted-foreground group-hover:text-foreground" />
      </div>
    </>
  );
}

export { SearchInputWithSuggestions }; 