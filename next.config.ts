import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  
  // Exclude stagewise from production builds
  webpack: (config, { dev, isServer }) => {
    if (!dev) {
      // In production, replace stagewise imports with empty modules
      config.resolve.alias = {
        ...config.resolve.alias,
        '@stagewise/toolbar-next': false,
      };
    }
    return config;
  },
};

export default nextConfig;
