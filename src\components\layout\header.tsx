'use client';

import * as React from 'react';
import { Settings, CreditCard, LogIn } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SearchInputWithSuggestions } from '@/components/ui/search-input-with-suggestions';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { APP_NAME } from '@/constants';
import { cn } from '@/utils';
import { SearchSuggestion, NewWebsiteSuggestion, ExistingWebsiteSuggestion } from '@/types';

interface HeaderProps {
  className?: string;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  onSearchClear?: () => void;
  suggestions?: SearchSuggestion[];
  showSuggestions?: boolean;
  searchLoading?: boolean;
  onSuggestionClick?: (suggestion: SearchSuggestion) => void;
  onAddWebsite?: (suggestion: NewWebsiteSuggestion) => void;
  onAddExistingWebsite?: (suggestion: ExistingWebsiteSuggestion) => void;
}

export function Header({ 
  className, 
  searchValue = '', 
  onSearchChange, 
  onSearchClear,
  suggestions = [],
  showSuggestions = false,
  searchLoading = false,
  onSuggestionClick,
  onAddWebsite,
  onAddExistingWebsite
}: HeaderProps) {
  return (
    <header className={cn(
      'sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60',
      className
    )}>
      <div className="container flex h-16 max-w-screen-2xl items-center justify-between px-4">
        {/* Left side - App name */}
        <div className="flex items-center space-x-2">
          <div 
            className="transition-colors hover:opacity-80 cursor-pointer"
            onClick={() => onSearchClear?.()}
          >
            <h1 className="text-xl font-bold text-primary">
              {APP_NAME}
            </h1>
          </div>
        </div>

        {/* Center - Search (hidden on mobile, visible on larger screens) */}
        <div className="hidden md:flex flex-1 justify-center px-6">
          <div className="w-full max-w-sm">
            <SearchInputWithSuggestions
              value={searchValue}
              onChange={onSearchChange || (() => {})}
              onClear={onSearchClear}
              placeholder="Search websites..."
              suggestions={suggestions}
              showSuggestions={showSuggestions}
              loading={searchLoading}
              onSuggestionClick={onSuggestionClick || (() => {})}
              onAddWebsite={onAddWebsite}
              onAddExistingWebsite={onAddExistingWebsite}
            />
          </div>
        </div>

        {/* Right side - Action buttons */}
        <div className="flex items-center space-x-2">
          {/* Theme toggle */}
          <ThemeToggle />
          
          {/* Settings */}
          <Button variant="ghost" size="icon">
            <Settings className="h-[1.2rem] w-[1.2rem]" />
            <span className="sr-only">Settings</span>
          </Button>
          
          {/* Upgrade to Pro */}
          <Button variant="outline" size="sm" className="hidden sm:flex">
            <CreditCard className="mr-2 h-4 w-4" />
            Upgrade to Pro
          </Button>
          
          {/* Login/Register */}
          <Button size="sm">
            <LogIn className="mr-2 h-4 w-4" />
            Login
          </Button>
        </div>
      </div>

      {/* Mobile search - shown below header on small screens */}
      <div className="md:hidden border-t bg-background px-4 py-3">
        <SearchInputWithSuggestions
          value={searchValue}
          onChange={onSearchChange || (() => {})}
          onClear={onSearchClear}
          placeholder="Search websites..."
          suggestions={suggestions}
          showSuggestions={showSuggestions}
          loading={searchLoading}
          onSuggestionClick={onSuggestionClick || (() => {})}
          onAddWebsite={onAddWebsite}
          onAddExistingWebsite={onAddExistingWebsite}
        />
      </div>
    </header>
  );
} 