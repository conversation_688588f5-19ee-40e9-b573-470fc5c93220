import { Website, Category, StorageData, Theme } from '@/types';
import { STORAGE_KEYS, STORAGE_VERSION } from '@/constants';

/**
 * Safely get data from localStorage with error handling
 */
function getStorageItem<T>(key: string, defaultValue: T): T {
  if (typeof window === 'undefined') {
    return defaultValue;
  }

  try {
    const item = localStorage.getItem(key);
    if (item === null) {
      return defaultValue;
    }
    return JSON.parse(item);
  } catch (error) {
    console.warn(`Error reading from localStorage key "${key}":`, error);
    return defaultValue;
  }
}

/**
 * Safely set data to localStorage with error handling
 */
function setStorageItem<T>(key: string, value: T): boolean {
  if (typeof window === 'undefined') {
    return false;
  }

  try {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error(`Error writing to localStorage key "${key}":`, error);
    return false;
  }
}

/**
 * Remove item from localStorage
 */
function removeStorageItem(key: string): boolean {
  if (typeof window === 'undefined') {
    return false;
  }

  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error(`Error removing localStorage key "${key}":`, error);
    return false;
  }
}

/**
 * Get websites from localStorage
 */
export function getStoredWebsites(): Website[] {
  const websites = getStorageItem<Website[]>(STORAGE_KEYS.WEBSITES, []);
  
  // Convert date strings back to Date objects
  return websites.map(website => ({
    ...website,
    createdAt: new Date(website.createdAt),
    updatedAt: new Date(website.updatedAt),
  }));
}

/**
 * Save websites to localStorage
 */
export function saveWebsites(websites: Website[]): boolean {
  return setStorageItem(STORAGE_KEYS.WEBSITES, websites);
}

/**
 * Get categories from localStorage
 */
export function getStoredCategories(): Category[] {
  const categories = getStorageItem<Category[]>(STORAGE_KEYS.CATEGORIES, []);
  
  // Convert date strings back to Date objects
  return categories.map(category => ({
    ...category,
    createdAt: new Date(category.createdAt),
    updatedAt: new Date(category.updatedAt),
  }));
}

/**
 * Save categories to localStorage
 */
export function saveCategories(categories: Category[]): boolean {
  return setStorageItem(STORAGE_KEYS.CATEGORIES, categories);
}

/**
 * Get theme from localStorage
 */
export function getStoredTheme(): Theme {  return getStorageItem<Theme>(STORAGE_KEYS.THEME, 'dark');}

/**
 * Save theme to localStorage
 */
export function saveTheme(theme: Theme): boolean {
  return setStorageItem(STORAGE_KEYS.THEME, theme);
}

/**
 * Get storage version
 */
export function getStorageVersion(): string {
  return getStorageItem<string>(STORAGE_KEYS.VERSION, '0.0.0');
}

/**
 * Save storage version
 */
export function saveStorageVersion(version: string): boolean {
  return setStorageItem(STORAGE_KEYS.VERSION, version);
}

/**
 * Export all data for backup
 */
export function exportData(): StorageData {
  return {
    websites: getStoredWebsites(),
    categories: getStoredCategories(),
    theme: getStoredTheme(),
    version: STORAGE_VERSION,
  };
}

/**
 * Import data from backup
 */
export function importData(data: StorageData): boolean {
  try {
    const success = [
      saveWebsites(data.websites),
      saveCategories(data.categories),
      saveTheme(data.theme),
      saveStorageVersion(data.version),
    ].every(Boolean);

    return success;
  } catch (error) {
    console.error('Error importing data:', error);
    return false;
  }
}

/**
 * Clear all stored data
 */
export function clearAllData(): boolean {
  try {
    const success = [
      removeStorageItem(STORAGE_KEYS.WEBSITES),
      removeStorageItem(STORAGE_KEYS.CATEGORIES),
      removeStorageItem(STORAGE_KEYS.THEME),
      removeStorageItem(STORAGE_KEYS.VERSION),
    ].every(Boolean);

    return success;
  } catch (error) {
    console.error('Error clearing data:', error);
    return false;
  }
}

/**
 * Check if data migration is needed
 */
export function needsMigration(): boolean {
  const currentVersion = getStorageVersion();
  return currentVersion !== STORAGE_VERSION;
}

/**
 * Migrate data to current version (placeholder for future migrations)
 */
export function migrateData(): boolean {
  const currentVersion = getStorageVersion();
  
  // Future migration logic will go here
  console.log(`Migrating data from version ${currentVersion} to ${STORAGE_VERSION}`);
  
  // For now, just update the version
  return saveStorageVersion(STORAGE_VERSION);
} 