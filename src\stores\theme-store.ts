import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { Theme, ThemeState } from '@/types';
import { getStoredTheme, saveTheme } from '@/utils/storage';
import { getSystemTheme, isBrowser } from '@/utils';

interface ThemeStore extends ThemeState {
  // Actions
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  initializeTheme: () => void;
}

export const useThemeStore = create<ThemeStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    theme: 'dark',
    systemTheme: 'dark',
    resolvedTheme: 'dark',

    // Actions
    setTheme: (theme: Theme) => {
      set({ theme });
      saveTheme(theme);
      
      // Update resolved theme
      const systemTheme = getSystemTheme();
      const resolvedTheme = theme === 'system' ? systemTheme : theme;
      
      set({ 
        systemTheme,
        resolvedTheme: resolvedTheme as 'light' | 'dark'
      });
      
      // Apply theme to document
      applyThemeToDocument(resolvedTheme as 'light' | 'dark');
    },

    toggleTheme: () => {
      const { theme, resolvedTheme } = get();
      
      if (theme === 'system') {
        // If currently system, toggle to opposite of resolved theme
        const newTheme = resolvedTheme === 'light' ? 'dark' : 'light';
        get().setTheme(newTheme);
      } else {
        // If light/dark, toggle to opposite
        const newTheme = theme === 'light' ? 'dark' : 'light';
        get().setTheme(newTheme);
      }
    },

    initializeTheme: () => {
      if (!isBrowser()) return;

      // Get stored theme or default
      const storedTheme = getStoredTheme();
      const systemTheme = getSystemTheme();
      const resolvedTheme = storedTheme === 'system' ? systemTheme : storedTheme;

      set({
        theme: storedTheme,
        systemTheme,
        resolvedTheme: resolvedTheme as 'light' | 'dark',
      });

      // Apply theme to document
      applyThemeToDocument(resolvedTheme as 'light' | 'dark');

      // Listen for system theme changes
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleSystemThemeChange = (e: MediaQueryListEvent) => {
        const newSystemTheme = e.matches ? 'dark' : 'light';
        const currentState = get();
        
        set({ systemTheme: newSystemTheme });
        
        // If theme is system, update resolved theme
        if (currentState.theme === 'system') {
          set({ resolvedTheme: newSystemTheme });
          applyThemeToDocument(newSystemTheme);
        }
      };

      mediaQuery.addEventListener('change', handleSystemThemeChange);
      
      // Cleanup function (for React components that need it)
      return () => {
        mediaQuery.removeEventListener('change', handleSystemThemeChange);
      };
    },
  }))
);

/**
 * Apply theme to document element
 */
function applyThemeToDocument(theme: 'light' | 'dark') {
  if (!isBrowser()) return;

  const root = document.documentElement;
  
  // Remove existing theme classes
  root.classList.remove('light', 'dark');
  
  // Add new theme class
  root.classList.add(theme);
  
  // Update meta theme-color for mobile browsers
  updateMetaThemeColor(theme);
}

/**
 * Update meta theme-color for mobile browsers
 */
function updateMetaThemeColor(theme: 'light' | 'dark') {
  if (!isBrowser()) return;

  let metaThemeColor = document.querySelector('meta[name="theme-color"]');
  
  if (!metaThemeColor) {
    metaThemeColor = document.createElement('meta');
    metaThemeColor.setAttribute('name', 'theme-color');
    document.head.appendChild(metaThemeColor);
  }
  
  // Set theme color based on theme - using LlamaFeed colors
  const color = theme === 'dark' ? '#111827' : '#ffffff'; // LlamaFeed dark : white
  metaThemeColor.setAttribute('content', color);
}

// Hook for easy theme access
export function useTheme() {
  const theme = useThemeStore((state) => state.theme);
  const resolvedTheme = useThemeStore((state) => state.resolvedTheme);
  const setTheme = useThemeStore((state) => state.setTheme);
  const toggleTheme = useThemeStore((state) => state.toggleTheme);

  return {
    theme,
    resolvedTheme,
    setTheme,
    toggleTheme,
  };
} 