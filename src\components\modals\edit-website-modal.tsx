'use client';

import { useState, useEffect, useCallback, memo } from 'react';
import { X, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useWebsites } from '@/stores/websites-store';
import { useCategories } from '@/stores/categories-store';
import { isValidUrl, normalizeUrl, getFaviconFallbacks } from '@/utils';
import { Website } from '@/types';
import React from 'react';

// Enhanced Favicon component with multiple fallbacks
interface FaviconImageProps {
  url: string;
  storedFaviconUrl?: string;
  alt: string;
  className?: string;
}

const FaviconImage = memo(function FaviconImage({ 
  url, 
  storedFaviconUrl,
  alt, 
  className = "w-6 h-6 rounded-sm" 
}: FaviconImageProps) {
  const [currentFaviconIndex, setCurrentFaviconIndex] = useState(0);
  const [hasError, setHasError] = useState(false);
  
  // Create fallback URLs, using stored favicon first if available
  const fallbackUrls = React.useMemo(() => {
    const generatedFallbacks = getFaviconFallbacks(url);
    return storedFaviconUrl 
      ? [storedFaviconUrl, ...generatedFallbacks.filter(u => u !== storedFaviconUrl)]
      : generatedFallbacks;
  }, [url, storedFaviconUrl]);
  
  const currentFaviconUrl = fallbackUrls[currentFaviconIndex] || '';

  const handleError = useCallback(() => {
    if (currentFaviconIndex < fallbackUrls.length - 1) {
      setCurrentFaviconIndex(currentFaviconIndex + 1);
    } else {
      setHasError(true);
    }
  }, [currentFaviconIndex, fallbackUrls.length]);

  const handleLoad = useCallback(() => {
    setHasError(false);
  }, []);

  // Reset when URLs change
  React.useEffect(() => {
    setCurrentFaviconIndex(0);
    setHasError(false);
  }, [url, storedFaviconUrl]);

  if (hasError || !currentFaviconUrl) {
    return (
      <div className={`${className} bg-muted rounded-sm flex items-center justify-center`}>
        <Globe className="h-3 w-3 text-muted-foreground" />
      </div>
    );
  }

  return (
    /* eslint-disable-next-line @next/next/no-img-element */
    <img 
      src={currentFaviconUrl}
      alt={alt}
      className={className}
      onError={handleError}
      onLoad={handleLoad}
    />
  );
});

interface EditWebsiteModalProps {
  isOpen: boolean;
  onClose: () => void;
  website: Website | null;
}

export function EditWebsiteModal({ isOpen, onClose, website }: EditWebsiteModalProps) {
  const [websiteName, setWebsiteName] = useState('');
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [websiteDescription, setWebsiteDescription] = useState('');
  const [selectedCategoryId, setSelectedCategoryId] = useState('');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { updateWebsite, moveWebsite, error: websiteError } = useWebsites();
  const { categories } = useCategories();

  // Reset form when modal opens/closes or website changes
  useEffect(() => {
    if (isOpen && website) {
      setWebsiteName(website.name);
      setWebsiteUrl(website.url);
      setWebsiteDescription(website.description || '');
      setSelectedCategoryId(website.categoryId);
      setError('');
      setIsSubmitting(false);
    }
  }, [isOpen, website]);

  // Display website error from store
  useEffect(() => {
    if (websiteError) {
      setError(websiteError);
    }
  }, [websiteError]);

  if (!isOpen || !website) return null;

  const handleSave = async () => {
    if (!websiteName.trim()) {
      setError('Website name is required');
      return;
    }

    if (!websiteUrl.trim()) {
      setError('Website URL is required');
      return;
    }

    if (!isValidUrl(websiteUrl)) {
      setError('Please enter a valid URL (e.g., https://example.com)');
      return;
    }

    if (!selectedCategoryId) {
      setError('Please select a category');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      // First update the website name and URL
      updateWebsite(website.id, {
        name: websiteName.trim(),
        url: normalizeUrl(websiteUrl),
        description: websiteDescription.trim() || undefined,
      });

      // If category changed, move the website
      if (selectedCategoryId !== website.categoryId) {
        moveWebsite(website.id, selectedCategoryId);
      }

      // Close modal on success
      if (!websiteError) {
        onClose();
      }
    } catch {
      setError('Failed to update website');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave();
    }
  };

  const currentCategory = categories.find(cat => cat.id === website.categoryId);
  const selectedCategory = categories.find(cat => cat.id === selectedCategoryId);
  const categoryChanged = selectedCategoryId !== website.categoryId;

  return (
    <div 
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div 
        className="bg-background sketchy-box-lg shadow-lg w-full max-w-md"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold">Edit Website</h2>
            <p className="text-sm text-muted-foreground mt-1">
              Update website name, URL, description, and category
            </p>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Category Selection */}
        <div className="p-6 border-b bg-muted/30">
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Category:
              {categoryChanged && (
                <span className="text-xs text-muted-foreground ml-2">
                  (Changed from &quot;{currentCategory?.name}&quot;)
                </span>
              )}
            </label>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategoryId(category.id)}
                  className={`px-3 py-1.5 text-sm font-medium transition-colors ${
                    selectedCategoryId === category.id
                      ? 'bg-primary text-primary-foreground sketchy-box-sm'
                      : 'bg-background border hover:bg-accent sketchy-box-sm'
                  }`}
                  style={{
                    borderColor: selectedCategoryId === category.id ? undefined : category.color,
                  }}
                >
                  {category.name}
                </button>
              ))}
            </div>
            {selectedCategory && (
              <p className="text-xs text-muted-foreground">
                {categoryChanged 
                  ? `Moving to &quot;${selectedCategory.name}&quot; category`
                  : `Staying in &quot;${selectedCategory.name}&quot; category`
                }
              </p>
            )}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="p-4 bg-destructive/10 text-destructive text-sm border-b">
            {error}
          </div>
        )}

        {/* Website Preview */}
        <div className="p-6 border-b">
          <div className="flex items-center space-x-3 p-3 sketchy-box bg-muted/30">
            <FaviconImage 
              url={websiteUrl || website.url} 
              storedFaviconUrl={website.faviconUrl}
              alt={websiteName || website.name} 
            />
            <div className="flex-1 min-w-0">
              <div className="font-medium text-lg truncate">
                {websiteName || 'Website Name'}
              </div>
              {(websiteDescription || website.description) && (
                <div className="text-sm text-muted-foreground truncate">
                  {websiteDescription || website.description}
                </div>
              )}
              <div className="text-sm text-muted-foreground truncate">
                {websiteUrl || website.url}
              </div>
              {selectedCategory && (
                <div className="text-xs text-muted-foreground mt-1">
                  {selectedCategory.name}
                  {categoryChanged && (
                    <span className="text-amber-600 dark:text-amber-400 ml-1">
                      (will be moved)
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="p-6 space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Website Name</label>
            <Input
              placeholder="e.g., My Portfolio"
              value={websiteName}
              onChange={(e) => setWebsiteName(e.target.value)}
              onKeyDown={handleKeyDown}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Website URL</label>
            <Input
              placeholder="https://example.com"
              value={websiteUrl}
              onChange={(e) => setWebsiteUrl(e.target.value)}
              onKeyDown={handleKeyDown}
            />
            <p className="text-xs text-muted-foreground">
              Include the full URL with https:// or http://
            </p>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Description (Optional)</label>
            <Input
              placeholder="Brief description of the website"
              value={websiteDescription}
              onChange={(e) => setWebsiteDescription(e.target.value)}
              onKeyDown={handleKeyDown}
            />
          </div>
        </div>

        {/* Actions */}
        <div className="flex space-x-3 p-6 pt-0">
          <Button variant="outline" onClick={onClose} className="flex-1">
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSubmitting || !websiteName.trim() || !websiteUrl.trim() || !selectedCategoryId}
            className="flex-1"
          >
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>
    </div>
  );
} 