import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>, Kalam } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/layout/theme-provider";
import { DataProvider } from "@/components/layout/data-provider";
import { HydrationBoundary } from "@/components/layout/hydration-boundary";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { APP_NAME } from "@/constants";
import { StagewiseToolbar } from "@stagewise/toolbar-next";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const kalam = Kalam({
  subsets: ["latin"],
  weight: ["300", "400", "700"],
  variable: "--font-kalam",
});

// Stagewise toolbar configuration
const stagewiseConfig = {
  plugins: []
};

export const metadata: Metadata = {
  title: `${APP_NAME} - Organize Your Tech Stack`,
  description: "A personalized tech stack organizer that helps you stay up-to-date with the latest technologies and manage your daily-use websites.",
  keywords: ["tech-stack", "organizer", "productivity", "websites", "tools"],
  authors: [{ name: "StackZest Team" }],
  creator: "StackZest Team",
  publisher: "StackZest",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      {/* 
        suppressHydrationWarning is used here to prevent hydration errors
        caused by browser extensions (like VS Code) that add classes to the body element
        This is safe because it only suppresses warnings for direct children of html/body
      */}
      <body className={`${inter.variable} ${kalam.variable} font-handwritten antialiased`} suppressHydrationWarning>
        <ErrorBoundary>
          <HydrationBoundary>
            <ThemeProvider>
              <DataProvider>
                {children}
              </DataProvider>
            </ThemeProvider>
          </HydrationBoundary>
        </ErrorBoundary>
        
        {/* Stagewise toolbar - only in development */}
        {process.env.NODE_ENV === 'development' && (
          <StagewiseToolbar config={stagewiseConfig} />
        )}
      </body>
    </html>
  );
}
