import { PreDefinedWebsite, Category, PopupOptions } from '@/types';

// Application constants
export const APP_NAME = 'StackZest';
export const APP_VERSION = '1.0.0';
export const STORAGE_VERSION = '1.0.0';

// Local storage keys
export const STORAGE_KEYS = {
  WEBSITES: 'stackzest_websites',
  CATEGORIES: 'stackzest_categories',
  THEME: 'stackzest_theme',
  VERSION: 'stackzest_version',
} as const;

// Default categories
export const DEFAULT_CATEGORIES: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>[] = [
  {
    name: 'Everyday Use',
    color: '#3B82F6', // blue-500
    icon: 'Globe',
    order: 0,
  },
  {
    name: 'Privacy',
    color: '#10B981', // emerald-500
    icon: 'Shield',
    order: 1,
  },
  {
    name: 'Browser',
    color: '#F59E0B', // amber-500
    icon: 'Monitor',
    order: 2,
  },
  {
    name: 'Work',
    color: '#8B5CF6', // violet-500
    icon: 'Briefcase',
    order: 3,
  },
  {
    name: 'School',
    color: '#EF4444', // red-500
    icon: 'GraduationCap',
    order: 4,
  },
  {
    name: 'Hobbies',
    color: '#EC4899', // pink-500
    icon: 'Heart',
    order: 5,
  },
];

// Predefined popular websites
export const PREDEFINED_WEBSITES: PreDefinedWebsite[] = [
  // Social Media
  {
    name: 'YouTube',
    url: 'https://youtube.com',
    category: 'Everyday Use',
    description: 'Video sharing platform',
    faviconUrl: 'https://youtube.com/favicon.ico',
  },
  {
    name: 'Twitter/X',
    url: 'https://x.com',
    category: 'Everyday Use',
    description: 'Social media platform',
    faviconUrl: 'https://x.com/favicon.ico',
  },
  {
    name: 'Reddit',
    url: 'https://reddit.com',
    category: 'Everyday Use',
    description: 'Social news aggregation',
    faviconUrl: 'https://reddit.com/favicon.ico',
  },
  {
    name: 'Instagram',
    url: 'https://instagram.com',
    category: 'Everyday Use',
    description: 'Photo and video sharing',
    faviconUrl: 'https://instagram.com/favicon.ico',
  },
  {
    name: 'TikTok',
    url: 'https://tiktok.com',
    category: 'Everyday Use',
    description: 'Short-form video platform',
    faviconUrl: 'https://tiktok.com/favicon.ico',
  },

  // Privacy Tools
  {
    name: 'DuckDuckGo',
    url: 'https://duckduckgo.com',
    category: 'Privacy',
    description: 'Privacy-focused search engine',
    faviconUrl: 'https://duckduckgo.com/favicon.ico',
  },
  {
    name: 'ProtonMail',
    url: 'https://proton.me',
    category: 'Privacy',
    description: 'Encrypted email service',
    faviconUrl: 'https://proton.me/favicon.ico',
  },
  {
    name: 'Signal',
    url: 'https://signal.org',
    category: 'Privacy',
    description: 'Private messaging app',
    faviconUrl: 'https://signal.org/favicon.ico',
  },

  // Browsers
  {
    name: 'Chrome',
    url: 'https://chrome.google.com',
    category: 'Browser',
    description: 'Google Chrome browser',
    faviconUrl: 'https://chrome.google.com/favicon.ico',
  },
  {
    name: 'Firefox',
    url: 'https://firefox.com',
    category: 'Browser',
    description: 'Mozilla Firefox browser',
    faviconUrl: 'https://firefox.com/favicon.ico',
  },
  {
    name: 'Safari',
    url: 'https://apple.com/safari',
    category: 'Browser',
    description: 'Apple Safari browser',
    faviconUrl: 'https://apple.com/favicon.ico',
  },
  {
    name: 'Edge',
    url: 'https://microsoft.com/edge',
    category: 'Browser',
    description: 'Microsoft Edge browser',
    faviconUrl: 'https://microsoft.com/favicon.ico',
  },

  // Work Tools
  {
    name: 'GitHub',
    url: 'https://github.com',
    category: 'Work',
    description: 'Code hosting platform',
    faviconUrl: 'https://github.com/favicon.ico',
  },
  {
    name: 'Slack',
    url: 'https://slack.com',
    category: 'Work',
    description: 'Team communication',
    faviconUrl: 'https://slack.com/favicon.ico',
  },
  {
    name: 'Notion',
    url: 'https://notion.so',
    category: 'Work',
    description: 'All-in-one workspace',
    faviconUrl: 'https://notion.so/favicon.ico',
  },
  {
    name: 'Figma',
    url: 'https://figma.com',
    category: 'Work',
    description: 'Design collaboration tool',
    faviconUrl: 'https://figma.com/favicon.ico',
  },

  // Educational
  {
    name: 'Khan Academy',
    url: 'https://khanacademy.org',
    category: 'School',
    description: 'Free online learning',
    faviconUrl: 'https://khanacademy.org/favicon.ico',
  },
  {
    name: 'Coursera',
    url: 'https://coursera.org',
    category: 'School',
    description: 'Online courses',
    faviconUrl: 'https://coursera.org/favicon.ico',
  },
  {
    name: 'Stack Overflow',
    url: 'https://stackoverflow.com',
    category: 'School',
    description: 'Programming Q&A',
    faviconUrl: 'https://stackoverflow.com/favicon.ico',
  },
];

// Popup window default options
export const DEFAULT_POPUP_OPTIONS: PopupOptions = {
  width: 1200,
  height: 800,
  scrollbars: true,
  resizable: true,
  menubar: false,
  toolbar: false,
};

// Theme configuration
export const THEME_CONFIG = {
  SYSTEM_STORAGE_KEY: 'stackzest_theme',
  DEFAULT_THEME: 'system' as const,
  THEME_CLASSES: {
    light: 'light',
    dark: 'dark',
  },
} as const;

// Search configuration
export const SEARCH_CONFIG = {
  DEBOUNCE_DELAY: 300,
  MIN_SEARCH_LENGTH: 2,
  MAX_SEARCH_RESULTS: 50,
} as const;

// UI Constants
export const UI_CONFIG = {
  MAX_WEBSITES_PREVIEW: 3,
  ANIMATION_DURATION: 200,
  DRAG_THRESHOLD: 5,
} as const;

// Animation durations (in milliseconds)
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
} as const;

// Breakpoints for responsive design
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const; 