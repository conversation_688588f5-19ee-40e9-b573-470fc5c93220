'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useDragDrop } from './drag-drop-provider';

export function CategoryDragOverlay() {
  const { isDraggingCategory, draggedCategory } = useDragDrop();

  return (
    <AnimatePresence>
      {isDraggingCategory && draggedCategory && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-40 bg-background/20 backdrop-blur-sm pointer-events-none"
        >
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
            <motion.div
              initial={{ y: -20, scale: 0.8 }}
              animate={{ y: 0, scale: 1 }}
              exit={{ y: -20, scale: 0.8 }}
              className="bg-background/90 backdrop-blur-md border border-border rounded-lg px-4 py-3 shadow-lg"
            >
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-primary rounded-full animate-pulse" />
                <span className="text-sm font-medium text-foreground">
                  Dragging &quot;{draggedCategory.name}&quot; category
                </span>
              </div>
              <div className="text-xs text-muted-foreground mt-1 text-center">
                Drop on any category to reorder
              </div>
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
} 