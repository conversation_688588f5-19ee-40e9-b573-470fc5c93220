'use client';

import React, { useState } from 'react';
import { GripVertical } from 'lucide-react';
// import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Category } from '@/types';
import { useDragDrop } from './drag-drop-provider';

interface DraggableCategoryWrapperProps {
  category: Category;
  children: React.ReactNode;
}

export function DraggableCategoryWrapper({
  category,
  children
}: DraggableCategoryWrapperProps) {
  const [isHovered, setIsHovered] = useState(false);
  const {
    startCategoryDrag,
    endDrag,
    isDraggingCategory,
    draggedCategory
  } = useDragDrop();

  const isDragging = isDraggingCategory && draggedCategory?.id === category.id;

  const handleDragStart = (e: React.DragEvent) => {
    console.log('🚀 Category drag start:', category.name);
    
    // Set basic drag data (required for HTML5 drag and drop)
    e.dataTransfer.setData('text/plain', category.id);
    e.dataTransfer.effectAllowed = 'move';
    
    // Start the drag in our context
    const rect = e.currentTarget.getBoundingClientRect();
    const startPosition = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
    
    console.log('   - Start position:', startPosition);
    startCategoryDrag(category, startPosition);
  };

  const handleDragEnd = () => {
    console.log('🛑 Category drag end:', category.name);
    endDrag();
  };

  // Note: Drag over and drop handling is now managed by CategoryGridWrapper
  // This component only handles drag start/end for the individual cards

  return (
    <div
      className="group relative cursor-grab active:cursor-grabbing"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      style={{
        transform: isDragging ? 'scale(1.05) rotate(2deg)' : 'scale(1) rotate(0deg)',
        zIndex: isDragging ? 50 : 1,
        filter: isDragging ? 'drop-shadow(0 20px 25px rgb(0 0 0 / 0.15))' : 'none',
        transition: 'all 0.2s ease-out',
      }}
    >
      {/* Drag handle */}
      <div
        className="absolute top-2 right-2 z-10 p-1 rounded-md bg-background/80 backdrop-blur-sm border border-border/50 text-muted-foreground hover:text-foreground transition-all duration-200"
        style={{
          opacity: isHovered || isDragging ? 0.9 : 0.3,
          transform: isHovered || isDragging ? 'scale(1)' : 'scale(0.9)',
        }}
        title="Drag to reorder categories"
      >
        <GripVertical className="h-4 w-4" />
      </div>

      {/* Category content */}
      <div className={`
        transition-all duration-200
        ${isDragging ? 'opacity-80' : 'opacity-100'}
      `}>
                 {children}
       </div>
     </div>
  );
} 