import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { Category, CategoryState } from '@/types';
import { getStoredCategories, saveCategories } from '@/utils/storage';
import { DEFAULT_CATEGORIES } from '@/constants';
import { generateId } from '@/utils';

interface CategoriesStore extends CategoryState {
  // Actions
  addCategory: (category: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateCategory: (id: string, updates: Partial<Category>) => void;
  deleteCategory: (id: string) => void;
  reorderCategories: (categories: Category[]) => void;
  resetCategories: () => void;
  initializeCategories: () => void;
  
  // Computed getters
  getCategoryById: (id: string) => Category | undefined;
  getCategoriesCount: () => number;
}

export const useCategoriesStore = create<CategoriesStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    categories: [],
    isLoading: false,
    error: null,

    // Actions
    addCategory: (categoryData) => {
      const { categories } = get();
      
      // Check for duplicate names
      const nameExists = categories.some(
        cat => cat.name.toLowerCase() === categoryData.name.toLowerCase()
      );
      
      if (nameExists) {
        set({ error: 'A category with this name already exists' });
        return;
      }

      const newCategory: Category = {
        ...categoryData,
        id: generateId(),
        order: categories.length,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const updatedCategories = [...categories, newCategory];
      
      set({ 
        categories: updatedCategories,
        error: null 
      });
      
      // Persist to localStorage
      saveCategories(updatedCategories);
    },

    updateCategory: (id, updates) => {
      const { categories } = get();
      
      // Check for duplicate names (excluding current category)
      if (updates.name) {
        const nameExists = categories.some(
          cat => cat.id !== id && cat.name.toLowerCase() === updates.name!.toLowerCase()
        );
        
        if (nameExists) {
          set({ error: 'A category with this name already exists' });
          return;
        }
      }

      const updatedCategories = categories.map(category =>
        category.id === id
          ? { ...category, ...updates, updatedAt: new Date() }
          : category
      );

      set({ 
        categories: updatedCategories,
        error: null 
      });
      
      // Persist to localStorage
      saveCategories(updatedCategories);
    },

    deleteCategory: (id) => {
      const { categories } = get();
      
      const updatedCategories = categories
        .filter(category => category.id !== id)
        .map((category, index) => ({
          ...category,
          order: index, // Reorder after deletion
          updatedAt: new Date(),
        }));

      set({ categories: updatedCategories });
      
      // Persist to localStorage
      saveCategories(updatedCategories);
    },

    reorderCategories: (newCategories) => {
      console.log('🏪 Store reorderCategories called with:', newCategories.length, 'categories');
      console.log('   - New order:', newCategories.map((c, i) => `${i}: ${c.name}`));
      
      const updatedCategories = newCategories.map((category, index) => ({
        ...category,
        order: index,
        updatedAt: new Date(),
      }));

      console.log('   - Setting categories in store...');
      set({ categories: updatedCategories });
      
      console.log('   - Persisting to localStorage...');
      // Persist to localStorage
      saveCategories(updatedCategories);
      console.log('✅ Store reorderCategories completed');
    },

    resetCategories: () => {
      const defaultCategories = DEFAULT_CATEGORIES.map((category, index) => ({
        ...category,
        id: generateId(),
        order: index,
        createdAt: new Date(),
        updatedAt: new Date(),
      }));

      set({ categories: defaultCategories });
      
      // Persist to localStorage
      saveCategories(defaultCategories);
    },

    initializeCategories: () => {
      set({ isLoading: true, error: null });
      
      try {
        const storedCategories = getStoredCategories();
        
        if (storedCategories.length === 0) {
          // No stored categories, use defaults
          const defaultCategories = DEFAULT_CATEGORIES.map((category, index) => ({
            ...category,
            id: generateId(),
            order: index,
            createdAt: new Date(),
            updatedAt: new Date(),
          }));
          
          set({ 
            categories: defaultCategories,
            isLoading: false 
          });
          
          // Save defaults to localStorage
          saveCategories(defaultCategories);
        } else {
          // Use stored categories
          set({ 
            categories: storedCategories,
            isLoading: false 
          });
        }
      } catch (error) {
        console.error('Error initializing categories:', error);
        set({ 
          error: 'Failed to load categories',
          isLoading: false 
        });
      }
    },

    // Computed getters
    getCategoryById: (id) => {
      const { categories } = get();
      return categories.find(category => category.id === id);
    },

    getCategoriesCount: () => {
      const { categories } = get();
      return categories.length;
    },
  }))
);

// Hook for easy access to categories
export function useCategories() {
  const categories = useCategoriesStore((state) => state.categories);
  const isLoading = useCategoriesStore((state) => state.isLoading);
  const error = useCategoriesStore((state) => state.error);
  const addCategory = useCategoriesStore((state) => state.addCategory);
  const updateCategory = useCategoriesStore((state) => state.updateCategory);
  const deleteCategory = useCategoriesStore((state) => state.deleteCategory);
  const reorderCategories = useCategoriesStore((state) => state.reorderCategories);
  const resetCategories = useCategoriesStore((state) => state.resetCategories);
  const getCategoryById = useCategoriesStore((state) => state.getCategoryById);

  return {
    categories,
    isLoading,
    error,
    addCategory,
    updateCategory,
    deleteCategory,
    reorderCategories,
    resetCategories,
    getCategoryById,
  };
} 