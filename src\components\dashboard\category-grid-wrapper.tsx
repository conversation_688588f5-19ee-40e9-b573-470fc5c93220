'use client';

import { motion } from 'framer-motion';
import { Category } from '@/types';
import { useDragDrop } from './drag-drop-provider';
import { DraggableCategoryWrapper } from './draggable-category-wrapper';

interface CategoryGridWrapperProps {
  categories: Category[];
  onReorderCategory: (categoryId: string, newIndex: number) => void;
  children: (category: Category, index: number) => React.ReactNode;
}

export function CategoryGridWrapper({
  categories,
  onReorderCategory,
  children,
}: CategoryGridWrapperProps) {
  const { 
    isDraggingCategory, 
    draggedCategory,
    dragOverCategoryId,
    handleCategoryDragOver,
    handleCategoryDragLeave
  } = useDragDrop();

  const handleDropAtIndex = (index: number) => {
    if (!isDraggingCategory || !draggedCategory || !onReorderCategory) return;

    const draggedIndex = categories.findIndex(c => c.id === draggedCategory.id);
    if (draggedIndex === -1) return;

    // Calculate the new position
    let newIndex = index;
    if (draggedIndex < index) {
      newIndex = index - 1; // Adjust for the removed item
    }

    console.log('🎯 Dropping at index:', newIndex, 'from:', draggedIndex);
    onReorderCategory(draggedCategory.id, newIndex);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 auto-rows-max">
      {categories.map((category, index) => (
        <div key={category.id} className="relative">
          {/* Drop zone before this category */}
          {isDraggingCategory && draggedCategory?.id !== category.id && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="absolute -top-3 left-0 right-0 z-30"
              onDragOver={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleCategoryDragOver(category.id, 'before');
              }}
              onDragLeave={() => handleCategoryDragLeave()}
              onDrop={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleDropAtIndex(index);
              }}
            >
              <div className={`
                h-2 w-full rounded-full transition-all duration-200
                ${dragOverCategoryId === category.id ? 'bg-primary/50 shadow-lg' : 'bg-transparent hover:bg-primary/20'}
              `}>
                {dragOverCategoryId === category.id && (
                  <motion.div
                    initial={{ scaleX: 0 }}
                    animate={{ scaleX: 1 }}
                    className="h-full w-full bg-primary rounded-full"
                  />
                )}
              </div>
              
              {dragOverCategoryId === category.id && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="absolute top-3 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-medium whitespace-nowrap shadow-lg"
                >
                  Drop here to insert before &quot;{category.name}&quot;
                </motion.div>
              )}
            </motion.div>
          )}

          {/* The actual category card */}
          <DraggableCategoryWrapper
            category={category}
          >
            {children(category, index)}
          </DraggableCategoryWrapper>

          {/* Drop zone after the last category */}
          {index === categories.length - 1 && isDraggingCategory && draggedCategory?.id !== category.id && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="absolute -bottom-3 left-0 right-0 z-30"
              onDragOver={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleCategoryDragOver(`after-${category.id}`, 'after');
              }}
              onDragLeave={() => handleCategoryDragLeave()}
              onDrop={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleDropAtIndex(categories.length);
              }}
            >
              <div className={`
                h-2 w-full rounded-full transition-all duration-200
                ${dragOverCategoryId === `after-${category.id}` ? 'bg-primary/50 shadow-lg' : 'bg-transparent hover:bg-primary/20'}
              `}>
                {dragOverCategoryId === `after-${category.id}` && (
                  <motion.div
                    initial={{ scaleX: 0 }}
                    animate={{ scaleX: 1 }}
                    className="h-full w-full bg-primary rounded-full"
                  />
                )}
              </div>
              
              {dragOverCategoryId === `after-${category.id}` && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-medium whitespace-nowrap shadow-lg"
                >
                  Drop here to add at end
                </motion.div>
              )}
            </motion.div>
          )}
        </div>
      ))}
    </div>
  );
} 