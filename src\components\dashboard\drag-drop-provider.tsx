'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Website, Category } from '@/types';

interface DragDropState {
  draggedWebsite: Website | null;
  draggedCategory: Category | null;
  dragOverCategoryId: string | null;
  dragOverWebsiteId: string | null;
  dropPosition: 'before' | 'after' | null;
  isDragging: boolean;
  isDraggingCategory: boolean;
  dragStartPosition: { x: number; y: number } | null;
}

interface DragDropContextType extends DragDropState {
  setDraggedWebsite: (website: Website | null) => void;
  setDraggedCategory: (category: Category | null) => void;
  setDragOverCategoryId: (categoryId: string | null) => void;
  setDragOverWebsiteId: (websiteId: string | null) => void;
  setDropPosition: (position: 'before' | 'after' | null) => void;
  setIsDragging: (isDragging: boolean) => void;
  setIsDraggingCategory: (isDraggingCategory: boolean) => void;
  startDrag: (website: Website) => void;
  startCategoryDrag: (category: Category, startPosition?: { x: number; y: number }) => void;
  endDrag: () => void;
  handleDragOver: (categoryId: string) => void;
  handleDragLeave: () => void;
  handleWebsiteDragOver: (websiteId: string, position: 'before' | 'after') => void;
  handleWebsiteDragLeave: () => void;
  handleCategoryDragOver: (categoryId: string, position: 'before' | 'after') => void;
  handleCategoryDragLeave: () => void;
}

const DragDropContext = createContext<DragDropContextType | null>(null);

interface DragDropProviderProps {
  children: ReactNode;
}

export function DragDropProvider({ children }: DragDropProviderProps) {
  const [state, setState] = useState<DragDropState>({
    draggedWebsite: null,
    draggedCategory: null,
    dragOverCategoryId: null,
    dragOverWebsiteId: null,
    dropPosition: null,
    isDragging: false,
    isDraggingCategory: false,
    dragStartPosition: null,
  });

  const setDraggedWebsite = (website: Website | null) => {
    setState(prev => ({ ...prev, draggedWebsite: website }));
  };

  const setDraggedCategory = (category: Category | null) => {
    setState(prev => ({ ...prev, draggedCategory: category }));
  };

  const setDragOverCategoryId = (categoryId: string | null) => {
    setState(prev => ({ ...prev, dragOverCategoryId: categoryId }));
  };

  const setDragOverWebsiteId = (websiteId: string | null) => {
    setState(prev => ({ ...prev, dragOverWebsiteId: websiteId }));
  };

  const setDropPosition = (position: 'before' | 'after' | null) => {
    setState(prev => ({ ...prev, dropPosition: position }));
  };

  const setIsDragging = (isDragging: boolean) => {
    setState(prev => ({ ...prev, isDragging }));
  };

  const setIsDraggingCategory = (isDraggingCategory: boolean) => {
    setState(prev => ({ ...prev, isDraggingCategory }));
  };

  const startDrag = (website: Website) => {
    setState({
      draggedWebsite: website,
      draggedCategory: null,
      dragOverCategoryId: null,
      dragOverWebsiteId: null,
      dropPosition: null,
      isDragging: true,
      isDraggingCategory: false,
      dragStartPosition: null,
    });
  };

  const startCategoryDrag = (category: Category, startPosition?: { x: number; y: number }) => {
    setState({
      draggedWebsite: null,
      draggedCategory: category,
      dragOverCategoryId: null,
      dragOverWebsiteId: null,
      dropPosition: null,
      isDragging: false,
      isDraggingCategory: true,
      dragStartPosition: startPosition || null,
    });
  };

  const endDrag = () => {
    setState({
      draggedWebsite: null,
      draggedCategory: null,
      dragOverCategoryId: null,
      dragOverWebsiteId: null,
      dropPosition: null,
      isDragging: false,
      isDraggingCategory: false,
      dragStartPosition: null,
    });
  };

  const handleDragOver = (categoryId: string) => {
    if (state.isDragging) {
      setDragOverCategoryId(categoryId);
      // Clear website-specific drag over when dragging over category
      setDragOverWebsiteId(null);
      setDropPosition(null);
    }
  };

  const handleDragLeave = () => {
    setDragOverCategoryId(null);
    setDragOverWebsiteId(null);
    setDropPosition(null);
  };

  const handleWebsiteDragOver = (websiteId: string, position: 'before' | 'after') => {
    if (state.isDragging && state.draggedWebsite?.id !== websiteId) {
      setDragOverWebsiteId(websiteId);
      setDropPosition(position);
    }
  };

  const handleWebsiteDragLeave = () => {
    setDragOverWebsiteId(null);
    setDropPosition(null);
  };

  const handleCategoryDragOver = (categoryId: string, position: 'before' | 'after') => {
    if (state.isDraggingCategory && state.draggedCategory?.id !== categoryId) {
      setDragOverCategoryId(categoryId);
      setDropPosition(position);
    }
  };

  const handleCategoryDragLeave = () => {
    setDragOverCategoryId(null);
    setDropPosition(null);
  };

  const contextValue: DragDropContextType = {
    ...state,
    setDraggedWebsite,
    setDraggedCategory,
    setDragOverCategoryId,
    setDragOverWebsiteId,
    setDropPosition,
    setIsDragging,
    setIsDraggingCategory,
    startDrag,
    startCategoryDrag,
    endDrag,
    handleDragOver,
    handleDragLeave,
    handleWebsiteDragOver,
    handleWebsiteDragLeave,
    handleCategoryDragOver,
    handleCategoryDragLeave,
  };

  return (
    <DragDropContext.Provider value={contextValue}>
      {children}
    </DragDropContext.Provider>
  );
}

export function useDragDrop() {
  const context = useContext(DragDropContext);
  if (!context) {
    throw new Error('useDragDrop must be used within a DragDropProvider');
  }
  return context;
} 