'use client';

import React, { useState, useCallback, useMemo, memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, ChevronDown, ChevronUp } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Category, Website } from '@/types';
import { DraggableWebsiteCard } from './draggable-website-card';
import { useDragDrop } from './drag-drop-provider';
import { useContext, createContext } from 'react';

// Create a context to track if a category is being dragged
const CategoryDragContext = createContext(false);
export const CategoryDragProvider = CategoryDragContext.Provider;
export const useCategoryDrag = () => useContext(CategoryDragContext);

interface AnimatedCategoryCardProps {
  category: Category;
  websites: Website[];
  iconComponent: React.ComponentType<{ className?: string; style?: React.CSSProperties }>;
  onAddWebsite: (categoryId: string) => void;
  onOpenWebsite: (url: string) => void;
  onDeleteWebsite: (websiteId: string) => void;
  onEditWebsite?: (website: Website) => void;
  onMoveWebsite?: (websiteId: string, targetCategoryId: string) => void;
  onReorderWebsite?: (websiteId: string, targetWebsiteId: string, position: 'before' | 'after') => void;
  onMoveToPosition?: (websiteId: string, targetCategoryId: string, targetWebsiteId: string, position: 'before' | 'after') => void;
  onCardClick?: (category: Category) => void;
}

const containerVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.4,
      when: "beforeChildren",
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 }
};

const AnimatedCategoryCard = memo(function AnimatedCategoryCard({
  category,
  websites,
  iconComponent: IconComponent,
  onAddWebsite,
  onOpenWebsite,
  onDeleteWebsite,
  onEditWebsite,
  onMoveWebsite,
  onReorderWebsite,
  onMoveToPosition,
  onCardClick,
}: AnimatedCategoryCardProps) {
  const { dragOverCategoryId, dragOverWebsiteId, isDraggingCategory, handleDragOver, handleDragLeave } = useDragDrop();
  const isCategoryBeingDragged = useCategoryDrag();
  const isDragOver = dragOverCategoryId === category.id;
  
  // State to track if all websites should be shown
  const [showAllWebsites, setShowAllWebsites] = useState(false);
  
  // Determine how many websites to show - memoized for performance
  const websitesToShow = useMemo(() => 
    showAllWebsites ? websites : websites.slice(0, 5),
    [showAllWebsites, websites]
  );
  const hasMoreWebsites = useMemo(() => websites.length > 5, [websites.length]);

  // Track mouse down position to distinguish between click and drag
  const [mouseDownPosition, setMouseDownPosition] = useState<{ x: number; y: number } | null>(null);
  const [headerMouseDownPosition, setHeaderMouseDownPosition] = useState<{ x: number; y: number } | null>(null);

  const handleCardMouseDown = useCallback((e: React.MouseEvent) => {
    // Only track mouse down on the content area, not header
    setMouseDownPosition({ x: e.clientX, y: e.clientY });
  }, []);

  const handleHeaderMouseDown = useCallback((e: React.MouseEvent) => {
    // Track mouse down on header area
    setHeaderMouseDownPosition({ x: e.clientX, y: e.clientY });
  }, []);

  const handleCardClick = useCallback((e: React.MouseEvent) => {
    // Don't handle clicks if category is being dragged or if there's no click handler
    if (isCategoryBeingDragged || !onCardClick || !mouseDownPosition) return;

    // Calculate distance moved since mouse down
    const distance = Math.sqrt(
      Math.pow(e.clientX - mouseDownPosition.x, 2) + 
      Math.pow(e.clientY - mouseDownPosition.y, 2)
    );

    // Only treat as click if mouse didn't move much (< 5px)
    if (distance < 5) {
      onCardClick(category);
    }

    setMouseDownPosition(null);
  }, [isCategoryBeingDragged, onCardClick, mouseDownPosition, category]);

  const handleHeaderClick = useCallback((e: React.MouseEvent) => {
    // Don't handle clicks if category is being dragged or if there's no click handler
    if (isCategoryBeingDragged || !onCardClick || !headerMouseDownPosition) return;

    // Calculate distance moved since mouse down
    const distance = Math.sqrt(
      Math.pow(e.clientX - headerMouseDownPosition.x, 2) + 
      Math.pow(e.clientY - headerMouseDownPosition.y, 2)
    );

    // Only treat as click if mouse didn't move much (< 8px) - slightly higher threshold for header
    if (distance < 8) {
      onCardClick(category);
    }

    setHeaderMouseDownPosition(null);
  }, [isCategoryBeingDragged, onCardClick, headerMouseDownPosition, category]);

  return (
    <motion.div
      layout={!isCategoryBeingDragged}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="h-fit"
    >
      <Card 
        className={`
          group backdrop-blur-sm transition-all duration-300 sketchy-box
          ${isDragOver ? 'ring-2 ring-primary/50' : ''}
          ${isCategoryBeingDragged ? 'pointer-events-none select-none' : ''}
        `}
        role="region"
        aria-label={`${category.name} category with ${websites.length} websites`}
        style={{
          backgroundColor: `${category.color}08`,
          borderWidth: '2px',
          borderStyle: 'solid',
          borderColor: `${category.color}60`,
          pointerEvents: isCategoryBeingDragged ? 'none' : 'auto',
          userSelect: isCategoryBeingDragged ? 'none' : 'auto',
          // Enhanced styling with brighter borders
          background: `
            linear-gradient(135deg, 
              ${category.color}12 0%, 
              ${category.color}08 50%, 
              ${category.color}06 100%
            ), 
            var(--card)
          `,
          boxShadow: `
            0 4px 12px ${category.color}15, 
            0 0 0 2px ${category.color}40,
            inset 0 1px 0 ${category.color}15
          `
        }}
        onDragOver={(e) => {
          e.preventDefault();
          e.stopPropagation();
          // Only handle category drag over if we're not over a specific website and not dragging a category
          if (!dragOverWebsiteId && !isDraggingCategory && !isCategoryBeingDragged) {
            handleDragOver(category.id);
          }
        }}
        onDragLeave={(e) => {
          e.preventDefault();
          e.stopPropagation();
          // Only handle drag leave if we're actually leaving the card area
          const rect = e.currentTarget.getBoundingClientRect();
          const x = e.clientX;
          const y = e.clientY;
          
          if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
            handleDragLeave();
          }
        }}
        onDrop={(e) => {
          e.preventDefault();
          e.stopPropagation();
          
          try {
            const dragData = JSON.parse(e.dataTransfer.getData('application/json'));
            const draggedWebsiteId = dragData.websiteId;
            const draggedCategoryId = dragData.categoryId;
            
            console.log('📦 Drop event on category:', category.name);
            console.log('   draggedWebsiteId:', draggedWebsiteId);
            console.log('   draggedCategoryId:', draggedCategoryId);
            console.log('   targetCategoryId:', category.id);
            console.log('   dragOverWebsiteId:', dragOverWebsiteId);
            
            // Only move to category if we're dropping on a different category and not on a specific website and not dragging a category
            if (draggedWebsiteId && draggedCategoryId !== category.id && !dragOverWebsiteId && !isDraggingCategory && !isCategoryBeingDragged && onMoveWebsite) {
              console.log('✅ Calling onMoveWebsite:', draggedWebsiteId, '->', category.id);
              onMoveWebsite(draggedWebsiteId, category.id);
            }
                                } catch {
            // Fallback to old method if JSON parsing fails
            const draggedWebsiteId = e.dataTransfer.getData('text/plain');
            if (draggedWebsiteId && onMoveWebsite && !dragOverWebsiteId && !isDraggingCategory && !isCategoryBeingDragged) {
              console.log('✅ Fallback onMoveWebsite:', draggedWebsiteId, '->', category.id);
              onMoveWebsite(draggedWebsiteId, category.id);
            }
          }
          
          handleDragLeave();
        }}
      >
        <CardHeader 
          className={`pb-3 border-b transition-all duration-300 dark:bg-muted/40 ${onCardClick ? 'cursor-pointer' : ''}`}
          style={{
            backgroundColor: `${category.color}12`,
            borderBottomColor: `${category.color}25`,
            background: `
              linear-gradient(135deg, 
                ${category.color}20 0%, 
                ${category.color}15 50%, 
                ${category.color}12 100%
              )
            `,
            boxShadow: `inset 0 -1px 0 ${category.color}30`
          }}
          onMouseDown={handleHeaderMouseDown}
          onClick={handleHeaderClick}
        >
          <motion.div 
            className="flex items-center justify-between"
            variants={itemVariants}
          >
            <div className="flex items-center space-x-3">
              <motion.div 
                className="p-2 sketchy-box-sm shadow-sm"
                style={{ 
                  backgroundColor: `${category.color}35`, 
                  border: `1px solid ${category.color}50`,
                  boxShadow: `
                    0 2px 4px ${category.color}15,
                    0 0 0 1px ${category.color}40,
                    inset 0 1px 0 ${category.color}20
                  `
                }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                <IconComponent 
                  className="h-6 w-6" 
                  style={{ color: category.color }}
                />
              </motion.div>
              <CardTitle className="text-xl font-semibold text-foreground">{category.name}</CardTitle>
            </div>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
              className="absolute top-0 right-2 z-30"
            >
              <motion.div
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="transition-opacity"
                    onClick={useCallback((e) => {
                      e.stopPropagation();
                      onAddWebsite(category.id);
                    }, [onAddWebsite, category.id])}
                  >
                  <Plus className="h-5 w-5" />
                </Button>
              </motion.div>
            </motion.div>
          </motion.div>
        </CardHeader>

        <CardContent 
          className={`transition-all duration-300 dark:bg-background/60 ${onCardClick ? 'cursor-pointer' : ''}`}
          style={{
            backgroundColor: `${category.color}04`,
            background: `
              linear-gradient(135deg, 
                ${category.color}08 0%, 
                ${category.color}04 50%, 
                ${category.color}02 100%
              ), 
              var(--background)
            `,
            backdropFilter: 'blur(8px)'
          }}
          onMouseDown={handleCardMouseDown}
          onClick={handleCardClick}
        >
          <AnimatePresence mode="wait">
            {websites.length === 0 ? (
              <motion.div
                key="empty"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="text-center py-8"
              >
                <CardDescription>
                  No websites added yet. Click the + button in the header to add your first website.
                </CardDescription>
              </motion.div>
            ) : (
              <motion.div
                key="websites"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="space-y-2 pt-3"
              >
                <div className={isCategoryBeingDragged ? 'pointer-events-none select-none' : ''}>
                  <AnimatePresence>
                    {websitesToShow.map((website, index) => (
                      <motion.div
                        key={website.id}
                        variants={itemVariants}
                        initial="hidden"
                        animate="visible"
                        exit="hidden"
                        transition={{ delay: index * 0.05 }}
                        style={{ 
                          pointerEvents: isCategoryBeingDragged ? 'none' : 'auto',
                          userSelect: isCategoryBeingDragged ? 'none' : 'auto'
                        }}
                      >
                        <DraggableWebsiteCard
                          website={website}
                          onOpen={onOpenWebsite}
                          onDelete={onDeleteWebsite}
                          onEdit={onEditWebsite}
                          onMove={onMoveWebsite}
                          onReorder={onReorderWebsite}
                          onMoveToPosition={onMoveToPosition}
                        />
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>

                {/* Empty drop zone at the end of the list */}
                {websites.length > 0 && (
                  <motion.div
                    className={`
                      min-h-[8px] rounded-md transition-all duration-200
                      ${isDragOver && !dragOverWebsiteId ? 'border-2 border-dashed' : ''}
                    `}
                    style={{
                      backgroundColor: isDragOver && !dragOverWebsiteId ? `${category.color}15` : 'transparent',
                      borderColor: isDragOver && !dragOverWebsiteId ? `${category.color}50` : 'transparent'
                    }}
                    onDragOver={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleDragOver(category.id);
                    }}
                    onDrop={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      
                      try {
                        const dragData = JSON.parse(e.dataTransfer.getData('application/json'));
                        const draggedWebsiteId = dragData.websiteId;
                        const draggedCategoryId = dragData.categoryId;
                        
                                          // Move to end of category
                  if (draggedWebsiteId && draggedCategoryId !== category.id && !isDraggingCategory && !isCategoryBeingDragged && onMoveWebsite) {
                    console.log('✅ Dropping at end of category:', draggedWebsiteId, '->', category.id);
                    onMoveWebsite(draggedWebsiteId, category.id);
                  }
                      } catch {
                                          const draggedWebsiteId = e.dataTransfer.getData('text/plain');
                  if (draggedWebsiteId && !isDraggingCategory && !isCategoryBeingDragged && onMoveWebsite) {
                    onMoveWebsite(draggedWebsiteId, category.id);
                  }
                      }
                    }}
                  />
                )}

                {hasMoreWebsites && (
                  <motion.button
                    className="text-xs text-muted-foreground hover:text-foreground text-center pt-2 w-full transition-colors cursor-pointer flex items-center justify-center gap-1"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.3 }}
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowAllWebsites(!showAllWebsites);
                    }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {showAllWebsites ? (
                      <>
                        <ChevronUp className="h-3 w-3" />
                        Show less
                      </>
                    ) : (
                      <>
                        <ChevronDown className="h-3 w-3" />
                        +{websites.length - 5} more websites
                      </>
                    )}
                  </motion.button>
                )}


              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>
    </motion.div>
  );
});

export { AnimatedCategoryCard }; 