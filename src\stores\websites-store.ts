import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { Website, WebsiteState } from '@/types';
import { getStoredWebsites, saveWebsites } from '@/utils/storage';
import { generateId, normalizeUrl, generateFaviconUrl, isValidUrl } from '@/utils';

interface WebsitesStore extends WebsiteState {
  // Actions
  addWebsite: (website: Omit<Website, 'id' | 'createdAt' | 'updatedAt'> & { faviconUrl?: string }) => void;
  updateWebsite: (id: string, updates: Partial<Website>) => void;
  deleteWebsite: (id: string) => void;
  reorderWebsites: (categoryId: string, websites: Website[]) => void;
  moveWebsiteToCategory: (websiteId: string, newCategoryId: string) => void;
  deleteWebsitesByCategory: (categoryId: string) => void;
  initializeWebsites: () => void;
  
  // Computed getters
  getWebsiteById: (id: string) => Website | undefined;
  getWebsitesByCategory: (categoryId: string) => Website[];
  getWebsitesCount: () => number;
  getWebsitesBySearch: (query: string) => Website[];
}

export const useWebsitesStore = create<WebsitesStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    websites: [],
    isLoading: false,
    error: null,

    // Actions
    addWebsite: (websiteData) => {
      const { websites } = get();
      
      // Validate URL
      if (!isValidUrl(websiteData.url)) {
        set({ error: 'Please enter a valid URL' });
        return;
      }

      // Normalize URL
      const normalizedUrl = normalizeUrl(websiteData.url);
      
      // Check for duplicate URLs in the same category
      const urlExists = websites.some(
        site => site.categoryId === websiteData.categoryId && 
                site.url === normalizedUrl
      );
      
      if (urlExists) {
        set({ error: 'This website already exists in this category' });
        return;
      }

      // Get the order for new website in this category
      const categoryWebsites = websites.filter(site => site.categoryId === websiteData.categoryId);
      const nextOrder = categoryWebsites.length;

      const newWebsite: Website = {
        ...websiteData,
        id: generateId(),
        url: normalizedUrl,
        faviconUrl: websiteData.faviconUrl || generateFaviconUrl(normalizedUrl),
        order: nextOrder,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const updatedWebsites = [...websites, newWebsite];
      
      set({ 
        websites: updatedWebsites,
        error: null 
      });
      
      // Persist to localStorage
      saveWebsites(updatedWebsites);
    },

    updateWebsite: (id, updates) => {
      const { websites } = get();
      
      // If URL is being updated, validate and normalize it
      let finalUpdates = { ...updates };
      if (updates.url) {
        if (!isValidUrl(updates.url)) {
          set({ error: 'Please enter a valid URL' });
          return;
        }
        
        const normalizedUrl = normalizeUrl(updates.url);
        const currentWebsite = websites.find(site => site.id === id);
        
        // Check for duplicate URLs in the same category (excluding current website)
        const urlExists = websites.some(
          site => site.id !== id && 
                  site.categoryId === currentWebsite?.categoryId && 
                  site.url === normalizedUrl
        );
        
        if (urlExists) {
          set({ error: 'This website already exists in this category' });
          return;
        }
        
        finalUpdates = {
          ...finalUpdates,
          url: normalizedUrl,
          faviconUrl: generateFaviconUrl(normalizedUrl),
        };
      }

      const updatedWebsites = websites.map(website =>
        website.id === id
          ? { ...website, ...finalUpdates, updatedAt: new Date() }
          : website
      );

      set({ 
        websites: updatedWebsites,
        error: null 
      });
      
      // Persist to localStorage
      saveWebsites(updatedWebsites);
    },

    deleteWebsite: (id) => {
      const { websites } = get();
      const websiteToDelete = websites.find(site => site.id === id);
      
      if (!websiteToDelete) return;

      // Remove website and reorder remaining websites in the same category
      const updatedWebsites = websites
        .filter(website => website.id !== id)
        .map(website => {
          // Reorder websites in the same category
          if (website.categoryId === websiteToDelete.categoryId && website.order > websiteToDelete.order) {
            return {
              ...website,
              order: website.order - 1,
              updatedAt: new Date(),
            };
          }
          return website;
        });

      set({ websites: updatedWebsites });
      
      // Persist to localStorage
      saveWebsites(updatedWebsites);
    },

    reorderWebsites: (categoryId, reorderedWebsites) => {
      const { websites } = get();
      
      const updatedWebsites = websites.map(website => {
        if (website.categoryId === categoryId) {
          const reorderedWebsite = reorderedWebsites.find(rw => rw.id === website.id);
          if (reorderedWebsite) {
            return {
              ...website,
              order: reorderedWebsites.indexOf(reorderedWebsite),
              updatedAt: new Date(),
            };
          }
        }
        return website;
      });

      set({ websites: updatedWebsites });
      
      // Persist to localStorage
      saveWebsites(updatedWebsites);
    },

    moveWebsiteToCategory: (websiteId, newCategoryId) => {
      const { websites } = get();
      const websiteToMove = websites.find(site => site.id === websiteId);
      
      if (!websiteToMove || websiteToMove.categoryId === newCategoryId) {
        return;
      }

      // Check for duplicate URLs in the new category
      const urlExists = websites.some(
        site => site.categoryId === newCategoryId && 
                site.url === websiteToMove.url
      );
      
      if (urlExists) {
        set({ error: 'This website already exists in the destination category' });
        return;
      }

      // Get the order for the website in the new category
      const newCategoryWebsites = websites.filter(site => site.categoryId === newCategoryId);
      const newOrder = newCategoryWebsites.length;

      const updatedWebsites = websites.map(website => {
        if (website.id === websiteId) {
          // Move website to new category
          return {
            ...website,
            categoryId: newCategoryId,
            order: newOrder,
            updatedAt: new Date(),
          };
        } else if (website.categoryId === websiteToMove.categoryId && website.order > websiteToMove.order) {
          // Reorder remaining websites in the old category
          return {
            ...website,
            order: website.order - 1,
            updatedAt: new Date(),
          };
        }
        return website;
      });

      set({ 
        websites: updatedWebsites,
        error: null 
      });
      
      // Persist to localStorage
      saveWebsites(updatedWebsites);
    },

    deleteWebsitesByCategory: (categoryId) => {
      const { websites } = get();
      
      const updatedWebsites = websites.filter(website => website.categoryId !== categoryId);

      set({ websites: updatedWebsites });
      
      // Persist to localStorage
      saveWebsites(updatedWebsites);
    },

    initializeWebsites: () => {
      set({ isLoading: true, error: null });
      
      try {
        const storedWebsites = getStoredWebsites();
        
        set({ 
          websites: storedWebsites,
          isLoading: false 
        });
      } catch (error) {
        console.error('Error initializing websites:', error);
        set({ 
          error: 'Failed to load websites',
          isLoading: false 
        });
      }
    },

    // Computed getters
    getWebsiteById: (id) => {
      const { websites } = get();
      return websites.find(website => website.id === id);
    },

    getWebsitesByCategory: (categoryId) => {
      const { websites } = get();
      return websites
        .filter(website => website.categoryId === categoryId)
        .sort((a, b) => a.order - b.order);
    },

    getWebsitesCount: () => {
      const { websites } = get();
      return websites.length;
    },

    getWebsitesBySearch: (query) => {
      const { websites } = get();
      if (!query.trim()) return websites;
      
      const searchTerm = query.toLowerCase();
      return websites.filter(website =>
        website.name.toLowerCase().includes(searchTerm) ||
        website.url.toLowerCase().includes(searchTerm) ||
        (website.description && website.description.toLowerCase().includes(searchTerm))
      );
    },
  }))
);

// Hook for easy access to websites
export function useWebsites() {
  const websites = useWebsitesStore((state) => state.websites);
  const isLoading = useWebsitesStore((state) => state.isLoading);
  const error = useWebsitesStore((state) => state.error);
  const addWebsite = useWebsitesStore((state) => state.addWebsite);
  const updateWebsite = useWebsitesStore((state) => state.updateWebsite);
  const deleteWebsite = useWebsitesStore((state) => state.deleteWebsite);
  const reorderWebsites = useWebsitesStore((state) => state.reorderWebsites);
  const moveWebsiteToCategory = useWebsitesStore((state) => state.moveWebsiteToCategory);
  const deleteWebsitesByCategory = useWebsitesStore((state) => state.deleteWebsitesByCategory);
  const getWebsitesByCategory = useWebsitesStore((state) => state.getWebsitesByCategory);
  const getWebsitesBySearch = useWebsitesStore((state) => state.getWebsitesBySearch);

  return {
    websites,
    isLoading,
    error,
    addWebsite,
    updateWebsite,
    deleteWebsite,
    reorderWebsites,
    moveWebsite: moveWebsiteToCategory,
    deleteWebsitesByCategory,
    getWebsitesByCategory,
    getWebsitesBySearch,
  };
} 